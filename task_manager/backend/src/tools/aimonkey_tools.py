import os
import json
import shutil
import pandas as pd
import time
from pathlib import Path
from datetime import datetime
from smbclient import open_file, register_session, listdir
from smbprotocol.exceptions import SMBOSError
from task_manager.backend.src.config.env_conf import EnvConf
from task_manager.backend.src.tools.feishu_tools import FeishuBot
from task_manager.backend.src.tools.execel_tools import ExcelOperation
from task_manager.backend.src.tools.log_module import get_logger

CURRENT_PATH = os.path.dirname(__file__)
CURRENT_DIR = Path(__file__)
SRC_DIR = CURRENT_DIR.parent.parent
DATA_JSON_DIR = os.path.join(SRC_DIR, 'data', 'json')


class AIMonkeyTools:
    """
    AIMonkey异常信息处理工具
    """

    def __init__(self):
        self.server = "10.205.101.200"
        self.share = "osptlog1"
        self.username = ""  # 根据实际情况填写
        self.password = ""  # 根据实际情况填写
        self.local_base_dir = "D:\\Monkey"
        self.excel_operation = ExcelOperation()
        self.feishu_bot = FeishuBot()

        # 初始化日志器
        self.logger = get_logger("AIMonkey", log_dir="logs/aimonkey")

        # 重试配置
        self.retry_config = {
            'max_retries': 1,           # 最大重试次数
            'retry_delay': 2,           # 重试间隔（秒）
        }

        self.logger.info("AIMonkey工具初始化完成")
        self.logger.info(f"重试配置: {self.retry_config}")

    def configure_retry_settings(self, max_retries=None, retry_delay=None):
        """
        配置重试设置
        :param max_retries: 最大重试次数
        :param retry_delay: 重试间隔（秒）
        """
        if max_retries is not None:
            self.retry_config['max_retries'] = max_retries
        if retry_delay is not None:
            self.retry_config['retry_delay'] = retry_delay

        self.logger.info(f"重试配置已更新: {self.retry_config}")

    def setup_smb_session(self):
        """设置SMB会话"""
        try:
            register_session(self.server, username=self.username, password=self.password)
            self.logger.info(f"SMB会话已建立: {self.server}")
            return True
        except Exception as e:
            self.logger.error(f"SMB会话建立失败: {str(e)}")
            return False

    def extract_filename_from_file_info(self, file_info):
        """
        从file_info对象中安全提取文件名
        :param file_info: 文件信息对象
        :return: 文件名字符串，如果无法提取则返回None
        """
        try:
            # 尝试多种方式获取文件名
            if hasattr(file_info, 'name') and file_info.name:
                return str(file_info.name).strip()
            elif hasattr(file_info, 'filename') and file_info.filename:
                return str(file_info.filename).strip()
            elif hasattr(file_info, 'path') and file_info.path:
                # 从路径中提取文件名
                return os.path.basename(str(file_info.path)).strip()
            elif hasattr(file_info, 'file_name') and file_info.file_name:
                return str(file_info.file_name).strip()
            elif isinstance(file_info, str) and file_info.strip():
                return file_info.strip()
            elif hasattr(file_info, '__str__'):
                # 尝试转换为字符串
                potential_name = str(file_info).strip()
                # 避免返回对象的字符串表示（通常以<开头）
                if potential_name and not potential_name.startswith('<') and len(potential_name) < 255:
                    return potential_name

            return None

        except Exception as e:
            self.logger.error(f"提取文件名时出错: {str(e)}")
            return None

    def download_single_file(self, remote_file_path, local_file_path, file_name, max_retries=None, retry_delay=None):
        """
        直接下载单个文件，简化的下载逻辑
        :param remote_file_path: 远程文件路径
        :param local_file_path: 本地文件路径
        :param file_name: 文件名（用于显示）
        :param max_retries: 最大重试次数（如果为None则使用配置值）
        :param retry_delay: 重试间隔（秒）（如果为None则使用配置值）
        :return: 下载是否成功
        """
        # 使用配置的重试参数
        if max_retries is None:
            max_retries = self.retry_config['max_retries']
        if retry_delay is None:
            retry_delay = self.retry_config['retry_delay']

        # 创建文件下载操作日志器
        op_logger = self.logger.create_operation_logger(f"下载文件: {file_name}")

        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    self.logger.info(f"第 {attempt + 1} 次尝试下载文件: {file_name}")
                    op_logger.step(f"重试下载 (第 {attempt + 1} 次)")
                    time.sleep(retry_delay)  # 重试前等待

                op_logger.step("开始下载文件")

                # 确保本地目录存在
                local_dir = os.path.dirname(local_file_path)
                os.makedirs(local_dir, exist_ok=True)
                op_logger.step(f"创建本地目录: {local_dir}")

                # 直接下载文件，不检查锁定状态
                with open_file(remote_file_path, mode='rb') as remote_file:
                    # 获取文件大小（如果可能）
                    file_size = None
                    try:
                        remote_file.seek(0, 2)  # 移动到文件末尾
                        file_size = remote_file.tell()
                        remote_file.seek(0)  # 回到文件开头
                        op_logger.step(f"获取文件大小: {self.format_file_size(file_size)}")
                    except:
                        op_logger.step("文件大小: 未知", "warning")

                    # 下载文件
                    op_logger.step("开始文件传输")
                    with open(local_file_path, 'wb') as local_file:
                        downloaded_size = 0
                        chunk_size = 8192  # 8KB chunks

                        while True:
                            chunk = remote_file.read(chunk_size)
                            if not chunk:
                                break
                            local_file.write(chunk)
                            downloaded_size += len(chunk)

                            # 记录进度（如果知道文件大小）
                            if file_size and file_size > 0:
                                progress = (downloaded_size / file_size) * 100
                                if downloaded_size % (chunk_size * 100) == 0:  # 每100个chunk记录一次
                                    self.logger.debug(f"下载进度: {progress:.1f}% ({self.format_file_size(downloaded_size)}/{self.format_file_size(file_size)})")

                op_logger.step("文件传输完成")

                # 验证下载的文件
                if os.path.exists(local_file_path):
                    actual_size = os.path.getsize(local_file_path)
                    op_logger.step(f"下载完成: {self.format_file_size(actual_size)}")

                    # 如果知道原始文件大小，验证是否一致
                    if file_size and actual_size != file_size:
                        op_logger.step(f"文件大小不匹配 (期望: {file_size}, 实际: {actual_size})", "warning")
                        # 不返回失败，继续处理

                    self.logger.log_file_operation("下载", local_file_path, "成功", f"大小: {self.format_file_size(actual_size)}")
                    op_logger.success(f"文件下载成功: {file_name}")
                    return True
                else:
                    op_logger.failure("本地文件不存在")
                    if attempt < max_retries:
                        continue
                    return False

            except FileNotFoundError:
                self.logger.error(f"远程文件不存在: {remote_file_path}")
                op_logger.failure("远程文件不存在")
                return False

            except PermissionError:
                self.logger.error(f"权限错误: 无法访问 {remote_file_path}")
                op_logger.failure("权限错误")
                return False

            except Exception as e:
                # 统一处理所有异常，包括SMB错误
                if attempt < max_retries:
                    self.logger.warning(f"下载异常，等待 {retry_delay} 秒后重试: {file_name} - {str(e)}")
                    op_logger.step(f"下载异常，等待重试: {str(e)}", "warning")
                    # 清理可能的不完整文件
                    if os.path.exists(local_file_path):
                        try:
                            os.remove(local_file_path)
                            self.logger.debug(f"已清理不完整文件: {local_file_path}")
                        except Exception as cleanup_error:
                            self.logger.debug(f"清理不完整文件失败: {cleanup_error}")
                    continue
                else:
                    self.logger.exception(f"下载文件异常: {file_name}")
                    op_logger.failure(f"下载异常: {str(e)}")
                    # 清理可能的不完整文件
                    if os.path.exists(local_file_path):
                        try:
                            os.remove(local_file_path)
                            self.logger.info(f"已清理不完整文件: {local_file_path}")
                        except Exception as cleanup_error:
                            self.logger.warning(f"清理不完整文件失败: {cleanup_error}")
                    return False

        # 如果所有重试都失败了
        self.logger.error(f"文件下载最终失败: {file_name}")
        op_logger.failure(f"所有重试都失败")
        return False

    def format_file_size(self, size_bytes):
        """
        格式化文件大小显示
        :param size_bytes: 字节数
        :return: 格式化的大小字符串
        """
        if size_bytes is None:
            return "未知"

        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"



    def verify_downloaded_files(self, file_list):
        """
        验证下载的文件
        :param file_list: 文件路径列表
        :return: 验证结果字典
        """
        # 创建文件验证操作日志器
        op_logger = self.logger.create_operation_logger("文件验证")
        op_logger.step(f"开始验证 {len(file_list)} 个文件")

        verification_results = {
            'total': len(file_list),
            'valid': 0,
            'invalid': 0,
            'missing': 0,
            'details': []
        }

        for file_path in file_list:
            result = {
                'path': file_path,
                'name': os.path.basename(file_path),
                'exists': False,
                'size': 0,
                'readable': False,
                'status': 'unknown'
            }

            try:
                # 检查文件是否存在
                if os.path.exists(file_path):
                    result['exists'] = True
                    result['size'] = os.path.getsize(file_path)

                    # 检查文件是否可读
                    try:
                        with open(file_path, 'rb') as f:
                            f.read(1)  # 尝试读取1字节
                        result['readable'] = True

                        # 检查文件大小是否合理
                        if result['size'] > 0:
                            result['status'] = 'valid'
                            verification_results['valid'] += 1
                            self.logger.info(f"✓ {result['name']} - {self.format_file_size(result['size'])}")
                        else:
                            result['status'] = 'empty'
                            verification_results['invalid'] += 1
                            self.logger.warning(f"⚠️  {result['name']} - 文件为空")

                    except Exception as e:
                        result['status'] = 'unreadable'
                        verification_results['invalid'] += 1
                        self.logger.error(f"✗ {result['name']} - 无法读取: {str(e)}")

                else:
                    result['status'] = 'missing'
                    verification_results['missing'] += 1
                    self.logger.error(f"✗ {result['name']} - 文件不存在")

            except Exception as e:
                result['status'] = 'error'
                verification_results['invalid'] += 1
                self.logger.error(f"✗ {result['name']} - 验证错误: {str(e)}")

            verification_results['details'].append(result)

        # 输出验证统计
        self.logger.info(f"\n验证结果:")
        self.logger.info(f"  总文件数: {verification_results['total']}")
        self.logger.info(f"  有效文件: {verification_results['valid']}")
        self.logger.info(f"  无效文件: {verification_results['invalid']}")
        self.logger.info(f"  缺失文件: {verification_results['missing']}")

        op_logger.success(f"验证完成: 有效 {verification_results['valid']}/{verification_results['total']} 个文件")
        return verification_results
    
    def download_files_from_smb(self, remote_path, local_path, file_extensions=None):
        """
        从SMB共享下载文件，简化版本
        :param remote_path: 远程路径
        :param local_path: 本地路径
        :param file_extensions: 文件扩展名列表，如['.xlsx', '.txt']
        """
        if file_extensions is None:
            file_extensions = ['.xlsx', '.txt', '.xls']

        # 创建SMB下载操作日志器
        op_logger = self.logger.create_operation_logger(f"SMB文件下载: {remote_path}")

        try:
            # 确保本地目录存在
            os.makedirs(local_path, exist_ok=True)
            op_logger.step(f"创建本地目录: {local_path}")

            # 远程目录路径
            remote_dir = f"\\\\{self.server}\\{self.share}\\{remote_path}"
            op_logger.step(f"连接远程目录: {remote_dir}")

            # 列出远程目录中的文件
            try:
                files = listdir(remote_dir)
                self.logger.info(f"找到 {len(files)} 个文件/目录")
                op_logger.step(f"扫描到 {len(files)} 个文件/目录")
            except Exception as e:
                self.logger.error(f"列出目录失败 {remote_dir}: {str(e)}")
                op_logger.failure(f"列出目录失败: {str(e)}")
                return []

            downloaded_files = []

            # 检查files的类型和内容
            if not files:
                self.logger.warning(f"目录为空: {remote_dir}")
                op_logger.step("目录为空", "warning")
                return downloaded_files
            
            for file_info in files:
                # 使用辅助方法安全获取文件名
                file_name = self.extract_filename_from_file_info(file_info)
                self.logger.info(f"file_name: {file_name}")
                # 如果无法获取文件名，记录详细信息并跳过
                if not file_name:
                    self.logger.warning(f"无法获取文件名，对象类型: {type(file_info)}")

                    # 显示对象的详细信息（用于调试）
                    if hasattr(file_info, '__dict__'):
                        self.logger.debug(f"对象属性: {file_info.__dict__}")
                    else:
                        available_attrs = [attr for attr in dir(file_info) if not attr.startswith('_')]
                        self.logger.debug(f"可用属性: {available_attrs[:10]}...")  # 只显示前10个属性

                        # 尝试获取一些常见属性的值
                        for attr in ['name', 'filename', 'path', 'file_name']:
                            if hasattr(file_info, attr):
                                try:
                                    value = getattr(file_info, attr)
                                    self.logger.debug(f"  {attr}: {value} (类型: {type(value)})")
                                except Exception as e:
                                    self.logger.debug(f"  {attr}: 获取失败 - {str(e)}")
                    continue

                file_ext = os.path.splitext(file_name)[1].lower()

                # 检查文件扩展名
                if file_ext in file_extensions:
                    remote_file_path = f"{remote_dir}\\{file_name}"
                    tmp = file_name.replace('.xls', '.xlsx')
                    local_file_path = os.path.join(local_path, tmp)
                    op_logger.step(f"准备下载到本地: {local_file_path}")

                    # 下载单个文件
                    success = self.download_single_file(
                        remote_file_path,
                        local_file_path,
                        file_name
                    )
                    if success:
                        downloaded_files.append(local_file_path)
                        op_logger.step(f"成功下载: {file_name}")
                    else:
                        self.logger.error(f"下载失败: {file_name}")
                        op_logger.step(f"下载失败: {file_name}", "error")
                else:
                    self.logger.debug(f"跳过文件（扩展名不匹配）: {file_name} ({file_ext})")

            op_logger.success(f"下载完成，成功下载 {len(downloaded_files)} 个文件")
            return downloaded_files

        except Exception as e:
            self.logger.exception(f"下载文件失败: {str(e)}")
            op_logger.failure(f"下载异常: {str(e)}")
            return []
    
    def download_aimonkey_files(self, target_date=None):
        """
        下载AIMonkey相关文件
        :param target_date: 目标日期，格式为YYYY-MM-DD，如果为None则使用当前日期
        :return: 下载的文件列表，如果失败返回空列表
        """
        # 创建主下载操作日志器
        op_logger = self.logger.create_operation_logger("AIMonkey文件下载")

        self.logger.info("=" * 60)
        self.logger.info("开始下载AIMonkey文件")
        self.logger.info("=" * 60)

        if not self.setup_smb_session():
            self.logger.error("SMB会话建立失败，无法下载文件")
            op_logger.failure("SMB会话建立失败")
            return []

        # 获取目标日期
        if target_date is None:
            target_date = datetime.now().strftime('%Y-%m-%d')

        self.logger.info(f"目标日期: {target_date}")
        self.logger.info(f"本地基础目录: {self.local_base_dir}")
        op_logger.step(f"设置目标日期: {target_date}")

        # 定义要下载的路径
        paths_to_download = [
            {
                'name': 'tOS15.1',
                'remote': f'tOS15.1\\AIMonkey\\{target_date}',
                'local': os.path.join(self.local_base_dir, 'tOS15.1', 'AIMonkey', target_date)
            },
            {
                'name': 'tOS16.0',
                'remote': f'tOS16.0\\AIMonkey\\{target_date}',
                'local': os.path.join(self.local_base_dir, 'tOS16.0', 'AIMonkey', target_date)
            }
        ]

        all_downloaded_files = []
        total_success = 0
        total_failed = 0

        for i, path_info in enumerate(paths_to_download, 1):
            self.logger.info(f"\n[{i}/{len(paths_to_download)}] 处理目录: {path_info['name']}")
            self.logger.info(f"远程路径: {path_info['remote']}")
            self.logger.info(f"本地路径: {path_info['local']}")
            op_logger.step(f"处理目录 {path_info['name']}")

            downloaded_files = self.download_files_from_smb(
                path_info['remote'],
                path_info['local']
            )

            if downloaded_files:
                all_downloaded_files.extend(downloaded_files)
                total_success += len(downloaded_files)
                self.logger.info(f"✓ 成功下载 {len(downloaded_files)} 个文件")
                op_logger.step(f"成功下载 {len(downloaded_files)} 个文件")
            else:
                self.logger.warning(f"该目录没有下载到文件")
                op_logger.step(f"目录 {path_info['name']} 无文件下载", "warning")

        # 记录下载统计
        self.logger.log_batch_operation("文件下载", len(paths_to_download), total_success, total_failed)

        self.logger.info("\n" + "=" * 60)
        self.logger.info("下载完成统计")
        self.logger.info("=" * 60)
        self.logger.info(f"总共下载: {total_success} 个文件")
        self.logger.info(f"下载失败: {total_failed} 个文件")
        self.logger.info(f"目标目录数: {len(paths_to_download)}")

        if all_downloaded_files:
            self.logger.info(f"\n下载的文件列表:")
            for file_path in all_downloaded_files:
                file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
                self.logger.info(f"  - {os.path.basename(file_path)} ({self.format_file_size(file_size)})")
                self.logger.debug(f"    路径: {file_path}")
        else:
            self.logger.warning("没有下载到任何文件")

        op_logger.success(f"下载完成，总计 {total_success} 个文件")
        return all_downloaded_files
    
    def find_target_excel_files(self, directory, target_date=None, pattern_keywords=None):
        """
        查找匹配条件的Excel文件（优化版本）
        :param directory: 搜索目录
        :param target_date: 目标日期，格式为YYYY-MM-DD，如果为None则使用当前日期
        :param pattern_keywords: 额外的匹配关键词列表，如['MonkeyAEE', 'Result']
        :return: 匹配的Excel文件列表
        """
        # 创建文件搜索操作日志器
        op_logger = self.logger.create_operation_logger(f"搜索Excel文件: {os.path.basename(directory)}")

        target_files = []

        if not os.path.exists(directory):
            self.logger.warning(f"目录不存在: {directory}")
            op_logger.failure("目录不存在")
            return target_files

        # 获取目标日期并转换为文件名格式（YYYYMMDD）
        if target_date is None:
            target_date = datetime.now().strftime('%Y-%m-%d')

        # 将日期格式从YYYY-MM-DD转换为YYYYMMDD
        date_suffix = target_date.replace('-', '')
        op_logger.step(f"搜索日期后缀: {date_suffix}")

        # 设置默认的匹配关键词
        if pattern_keywords is None:
            pattern_keywords = ['MonkeyAEE', 'Result', 'AIMonkey']

        self.logger.info(f"搜索条件:")
        self.logger.info(f"  目录: {directory}")
        self.logger.info(f"  日期后缀: {date_suffix}")
        self.logger.info(f"  关键词: {pattern_keywords}")
        op_logger.step(f"设置搜索条件: 日期={date_suffix}, 关键词={pattern_keywords}")

        for root, dirs, files in os.walk(directory):
            for file in files:
                # 检查文件扩展名
                if not (file.lower().endswith('.xls') or file.lower().endswith('.xlsx')):
                    continue

                file_matched = False
                match_reasons = []

                # 方式1: 检查是否以日期结尾
                if file.endswith(f'{date_suffix}.xls') or file.endswith(f'{date_suffix}.xlsx'):
                    file_matched = True
                    match_reasons.append(f"以日期结尾: {date_suffix}")

                # 方式2: 检查是否包含日期和关键词
                if date_suffix in file:
                    for keyword in pattern_keywords:
                        if keyword.lower() in file.lower():
                            file_matched = True
                            match_reasons.append(f"包含日期和关键词: {date_suffix}, {keyword}")
                            break

                # 方式3: 特殊模式匹配（针对Result_None_None_MonkeyAEE_SH_YYYYMMDD.xls格式）
                import re
                special_pattern = rf'Result_.*_MonkeyAEE_.*_{date_suffix}\.(xls|xlsx)$'
                if re.search(special_pattern, file, re.IGNORECASE):
                    file_matched = True
                    match_reasons.append(f"特殊模式匹配: Result_*_MonkeyAEE_*_{date_suffix}")

                if file_matched:
                    full_path = os.path.join(root, file)
                    target_files.append(full_path)
                    self.logger.info(f"✓ 找到匹配文件: {file}")
                    self.logger.debug(f"  匹配原因: {', '.join(match_reasons)}")
                    self.logger.debug(f"  完整路径: {full_path}")

        self.logger.info(f"在 {directory} 中找到 {len(target_files)} 个匹配的Excel文件")

        # 显示找到的文件列表
        if target_files:
            self.logger.info("找到的文件列表:")
            for i, file_path in enumerate(target_files, 1):
                file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
                self.logger.info(f"  {i}. {os.path.basename(file_path)} ({self.format_file_size(file_size)})")
                self.logger.debug(f"     路径: {file_path}")

        op_logger.success(f"找到 {len(target_files)} 个匹配文件")
        return target_files

    def find_specific_file(self, directory, filename_pattern, case_sensitive=False):
        """
        查找特定文件名模式的文件
        :param directory: 搜索目录
        :param filename_pattern: 文件名模式（支持通配符*和?）
        :param case_sensitive: 是否区分大小写
        :return: 匹配的文件列表
        """
        # 创建特定文件搜索操作日志器
        op_logger = self.logger.create_operation_logger(f"搜索特定文件: {filename_pattern}")

        found_files = []

        if not os.path.exists(directory):
            self.logger.warning(f"目录不存在: {directory}")
            op_logger.failure("目录不存在")
            return found_files

        import fnmatch

        self.logger.info(f"搜索特定文件:")
        self.logger.info(f"  目录: {directory}")
        self.logger.info(f"  模式: {filename_pattern}")
        self.logger.info(f"  区分大小写: {case_sensitive}")
        op_logger.step(f"搜索模式: {filename_pattern}")

        for root, dirs, files in os.walk(directory):
            for file in files:
                # 根据是否区分大小写进行匹配
                if case_sensitive:
                    if fnmatch.fnmatch(file, filename_pattern):
                        full_path = os.path.join(root, file)
                        found_files.append(full_path)
                        self.logger.info(f"✓ 找到匹配文件: {file}")
                        self.logger.debug(f"  完整路径: {full_path}")
                else:
                    if fnmatch.fnmatch(file.lower(), filename_pattern.lower()):
                        full_path = os.path.join(root, file)
                        found_files.append(full_path)
                        self.logger.info(f"✓ 找到匹配文件: {file}")
                        self.logger.debug(f"  完整路径: {full_path}")

        self.logger.info(f"在 {directory} 中找到 {len(found_files)} 个匹配的文件")

        # 显示找到的文件详情
        if found_files:
            self.logger.info("找到的文件详情:")
            for i, file_path in enumerate(found_files, 1):
                try:
                    file_size = os.path.getsize(file_path)
                    file_mtime = os.path.getmtime(file_path)
                    file_time = datetime.fromtimestamp(file_mtime).strftime('%Y-%m-%d %H:%M:%S')
                    self.logger.info(f"  {i}. {os.path.basename(file_path)}")
                    self.logger.info(f"     大小: {self.format_file_size(file_size)}")
                    self.logger.info(f"     修改时间: {file_time}")
                    self.logger.debug(f"     完整路径: {file_path}")
                except Exception as e:
                    self.logger.warning(f"  {i}. {os.path.basename(file_path)} (获取文件信息失败: {str(e)})")

        op_logger.success(f"找到 {len(found_files)} 个匹配文件")
        return found_files

    def search_files_by_keywords(self, directory, keywords, file_extensions=None, target_date=None):
        """
        根据关键词搜索文件（增强版本）
        :param directory: 搜索目录
        :param keywords: 关键词列表
        :param file_extensions: 文件扩展名列表，如['.xls', '.xlsx']
        :param target_date: 目标日期，格式为YYYY-MM-DD
        :return: 匹配的文件列表
        """
        # 创建关键词搜索操作日志器
        op_logger = self.logger.create_operation_logger(f"关键词搜索: {', '.join(keywords)}")

        if file_extensions is None:
            file_extensions = ['.xls', '.xlsx', '.txt', '.log']

        found_files = []

        if not os.path.exists(directory):
            self.logger.warning(f"目录不存在: {directory}")
            op_logger.failure("目录不存在")
            return found_files

        # 获取目标日期格式
        date_patterns = []
        if target_date:
            # YYYY-MM-DD -> YYYYMMDD
            date_patterns.append(target_date.replace('-', ''))
            # YYYY-MM-DD -> YYYY_MM_DD
            date_patterns.append(target_date.replace('-', '_'))
            # YYYY-MM-DD -> YYYY.MM.DD
            date_patterns.append(target_date.replace('-', '.'))

        self.logger.info(f"关键词搜索配置:")
        self.logger.info(f"  目录: {directory}")
        self.logger.info(f"  关键词: {keywords}")
        self.logger.info(f"  扩展名: {file_extensions}")
        self.logger.info(f"  日期模式: {date_patterns}")
        op_logger.step(f"搜索关键词: {keywords}, 扩展名: {file_extensions}")

        for root, dirs, files in os.walk(directory):
            for file in files:
                # 检查文件扩展名
                file_ext = os.path.splitext(file)[1].lower()
                if file_ext not in file_extensions:
                    continue

                file_lower = file.lower()
                match_score = 0
                match_details = []

                # 检查关键词匹配
                for keyword in keywords:
                    if keyword.lower() in file_lower:
                        match_score += 1
                        match_details.append(f"关键词: {keyword}")

                # 检查日期匹配
                if date_patterns:
                    for date_pattern in date_patterns:
                        if date_pattern in file:
                            match_score += 2  # 日期匹配权重更高
                            match_details.append(f"日期: {date_pattern}")
                            break

                # 如果有匹配，添加到结果中
                if match_score > 0:
                    full_path = os.path.join(root, file)
                    found_files.append({
                        'path': full_path,
                        'filename': file,
                        'score': match_score,
                        'match_details': match_details
                    })

        # 按匹配分数排序
        found_files.sort(key=lambda x: x['score'], reverse=True)

        self.logger.info(f"在 {directory} 中找到 {len(found_files)} 个匹配的文件")

        # 显示找到的文件（按分数排序）
        if found_files:
            self.logger.info("找到的文件（按匹配度排序）:")
            for i, file_info in enumerate(found_files, 1):
                try:
                    file_size = os.path.getsize(file_info['path'])
                    self.logger.info(f"  {i}. {file_info['filename']} (分数: {file_info['score']})")
                    self.logger.info(f"     大小: {self.format_file_size(file_size)}")
                    self.logger.info(f"     匹配: {', '.join(file_info['match_details'])}")
                    self.logger.debug(f"     路径: {file_info['path']}")
                except Exception as e:
                    self.logger.warning(f"  {i}. {file_info['filename']} (获取文件信息失败: {str(e)})")

        # 返回文件路径列表
        result_paths = [file_info['path'] for file_info in found_files]
        op_logger.success(f"找到 {len(result_paths)} 个匹配文件")
        return result_paths
    
    def get_package_owner_mapping(self):
        """
        获取Package包名与owner的映射关系
        """
        mapping_file = os.path.join(DATA_JSON_DIR, 'package_owner_mapping.json')
        
        if os.path.exists(mapping_file):
            with open(mapping_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # 默认映射关系，可以根据实际情况配置
            default_mapping = {
                "com.android.settings": "系统设置团队",
                "com.android.dialer": "拨号团队", 
                "com.android.contacts": "联系人团队",
                "com.android.camera": "相机团队",
                "com.android.gallery3d": "图库团队",
                "com.android.music": "音乐团队",
                "com.android.calculator2": "计算器团队",
                "com.android.calendar": "日历团队",
                "com.android.email": "邮件团队",
                "com.android.browser": "浏览器团队"
            }
            
            # 保存默认映射
            with open(mapping_file, 'w', encoding='utf-8') as f:
                json.dump(default_mapping, f, ensure_ascii=False, indent=4)
                
            return default_mapping
    
    def find_column_by_keywords(self, df, keywords, column_type=""):
        """
        根据关键词查找列名
        :param df: DataFrame对象
        :param keywords: 关键词列表
        :param column_type: 列类型描述（用于日志）
        :return: 匹配的列名或None
        """
        for col in df.columns:
            col_lower = str(col).lower()
            for keyword in keywords:
                if keyword.lower() in col_lower:
                    self.logger.info(f"  找到{column_type}列: '{col}'")
                    return col
        return None

    def detect_excel_format(self, file_path):
        """
        检测Excel文件的实际格式
        :param file_path: Excel文件路径
        :return: 实际格式 ('xls', 'xlsx', 'unknown')
        """
        try:
            with open(file_path, 'rb') as f:
                header = f.read(8)

            # 检查文件头部
            if header.startswith(b'PK\x03\x04'):
                return 'xlsx'  # ZIP格式 (Excel 2007+)
            elif header.startswith(b'\xd0\xcf\x11\xe0'):
                return 'xls'   # OLE格式 (Excel 97-2003)
            else:
                return 'unknown'
        except Exception:
            return 'unknown'

    def fix_excel_file_extension(self, file_path):
        """
        修复Excel文件扩展名不匹配的问题
        :param file_path: Excel文件路径
        :return: 修复后的文件路径
        """
        file_name, file_ext = os.path.splitext(file_path)
        actual_format = self.detect_excel_format(file_path)

        self.logger.info(f"  文件扩展名: {file_ext}")
        self.logger.info(f"  实际格式: {actual_format}")

        # 如果扩展名与实际格式不匹配，创建正确的副本
        if file_ext.lower() == '.xlsx' and actual_format == 'xls':
            # 文件实际是.xls格式，但扩展名是.xlsx
            correct_file_path = f"{file_name}_corrected.xls"
            try:
                import shutil
                shutil.copy2(file_path, correct_file_path)
                self.logger.info(f"  ✓ 创建格式正确的文件: {os.path.basename(correct_file_path)}")
                return correct_file_path
            except Exception as e:
                self.logger.warning(f"  ⚠️  创建副本失败: {str(e)}")
                return file_path

        elif file_ext.lower() == '.xls' and actual_format == 'xlsx':
            # 文件实际是.xlsx格式，但扩展名是.xls
            correct_file_path = f"{file_name}_corrected.xlsx"
            try:
                import shutil
                shutil.copy2(file_path, correct_file_path)
                self.logger.info(f"  ✓ 创建格式正确的文件: {os.path.basename(correct_file_path)}")
                return correct_file_path
            except Exception as e:
                self.logger.warning(f"  ⚠️  创建副本失败: {str(e)}")
                return file_path

        return file_path

    def analyze_excel_file(self, file_path):
        """
        分析Excel文件，提取Package包名和异常信息（pandas优化版本）
        :param file_path: Excel文件路径
        :return: 分析结果
        """
        # 创建Excel分析操作日志器
        op_logger = self.logger.create_operation_logger(f"Excel分析: {os.path.basename(file_path)}")

        self.logger.info(f"开始分析Excel文件: {os.path.basename(file_path)}")
        op_logger.step("开始分析文件")

        # 检测并修复文件格式问题
        self.logger.info(f"  检测文件格式...")
        op_logger.step("检测文件格式")
        corrected_file_path = self.fix_excel_file_extension(file_path)

        if corrected_file_path != file_path:
            self.logger.info(f"  使用修复后的文件: {os.path.basename(corrected_file_path)}")
            op_logger.step("使用修复后的文件")
            file_path = corrected_file_path

        try:
            # 智能选择读取引擎
            actual_format = self.detect_excel_format(file_path)
            file_ext = os.path.splitext(file_path)[1].lower()

            # 根据实际格式选择合适的引擎
            if actual_format == 'xlsx' or file_ext == '.xlsx':
                engine = 'openpyxl'
            elif actual_format == 'xls' or file_ext == '.xls':
                engine = 'xlrd'
            else:
                engine = 'openpyxl'  # 默认使用openpyxl

            self.logger.info(f"  使用引擎: {engine}")
            op_logger.step(f"使用 {engine} 引擎读取")

            # 尝试读取Excel文件
            try:
                df = pd.read_excel(
                    file_path,
                    engine=engine,
                    na_values=['', 'N/A', 'NULL', 'null', 'None'],
                    keep_default_na=True,
                    dtype=str
                )
            except Exception as e:
                # 如果第一个引擎失败，尝试另一个引擎
                self.logger.warning(f"  {engine} 引擎失败，尝试备用引擎: {str(e)}")
                op_logger.step(f"{engine} 引擎失败，尝试备用引擎", "warning")

                backup_engine = 'xlrd' if engine == 'openpyxl' else 'openpyxl'
                self.logger.info(f"  尝试备用引擎: {backup_engine}")

                df = pd.read_excel(
                    file_path,
                    engine=backup_engine,
                    na_values=['', 'N/A', 'NULL', 'null', 'None'],
                    keep_default_na=True,
                    dtype=str
                )

            self.logger.info(f"  文件读取成功，共 {len(df)} 行 {len(df.columns)} 列")
            self.logger.debug(f"  列名: {list(df.columns)}")
            op_logger.step(f"文件读取成功: {len(df)} 行 {len(df.columns)} 列")

            # 检查DataFrame是否为空
            if df.empty:
                self.logger.warning(f"  Excel文件为空")
                op_logger.failure("Excel文件为空")
                return []

            # 定义列名关键词
            package_keywords = ['package', 'pkg', '包名', 'packagename', 'app', 'application']
            causedby_keywords = ['causedby', 'caused', 'exception', 'error', 'err', '异常', '错误', 'message', 'msg']
            version_keywords = ['version', 'ver', '版本', 'build', 'release']
            path_keywords = ['path', '路径', 'file', 'filepath', 'location', 'dir', 'directory']

            # 智能查找列名
            package_column = self.find_column_by_keywords(df, package_keywords, "Package")
            causedby_column = self.find_column_by_keywords(df, causedby_keywords, "异常信息")
            version_column = self.find_column_by_keywords(df, version_keywords, "版本信息")
            path_column = self.find_column_by_keywords(df, path_keywords, "路径信息")

            self.logger.info(f"  列名识别结果:")
            self.logger.info(f"    Package列: {package_column}")
            self.logger.info(f"    CausedBy列: {causedby_column}")
            self.logger.info(f"    Version列: {version_column}")
            self.logger.info(f"    Path列: {path_column}")

            if not package_column:
                self.logger.error(f"  未找到Package相关列")
                self.logger.error(f"  可用列名: {list(df.columns)}")
                self.logger.error(f"  请确保Excel文件包含以下关键词之一的列: {package_keywords}")
                op_logger.failure("未找到Package相关列")
                return []

            # 为缺失的列设置默认值
            if not causedby_column:
                self.logger.warning(f"  未找到CausedBy列，将使用默认值")
                df['temp_causedby'] = '未知异常'
                causedby_column = 'temp_causedby'
                op_logger.step("未找到CausedBy列，使用默认值", "warning")

            if not version_column:
                self.logger.warning(f"  未找到Version列，将使用默认值")
                df['temp_version'] = '未知版本'
                version_column = 'temp_version'
                op_logger.step("未找到Version列，使用默认值", "warning")

            if not path_column:
                self.logger.warning(f"  未找到Path列，将使用默认值")
                df['temp_path'] = '未知路径'
                path_column = 'temp_path'
                op_logger.step("未找到Path列，使用默认值", "warning")

            # 数据清洗和预处理
            self.logger.info(f"  开始数据预处理...")
            op_logger.step("开始数据预处理")

            # 清理各列数据
            df[package_column] = df[package_column].fillna('').astype(str).str.strip()
            df[causedby_column] = df[causedby_column].fillna('未知异常').astype(str).str.strip()
            df[version_column] = df[version_column].fillna('未知版本').astype(str).str.strip()
            df[path_column] = df[path_column].fillna('未知路径').astype(str).str.strip()

            # 过滤掉空的Package行
            original_count = len(df)
            df = df[df[package_column] != '']
            filtered_count = len(df)

            if original_count != filtered_count:
                self.logger.info(f"  过滤掉 {original_count - filtered_count} 行空Package数据")
                op_logger.step(f"过滤空数据: {original_count - filtered_count} 行")

            if df.empty:
                self.logger.warning(f"  过滤后没有有效数据")
                op_logger.failure("过滤后没有有效数据")
                return []

            # 获取Package-Owner映射
            package_owner_mapping = self.get_package_owner_mapping()
            op_logger.step("加载Package-Owner映射")

            # 使用pandas向量化操作进行批量处理
            self.logger.info(f"  开始批量处理 {len(df)} 行数据...")
            op_logger.step(f"开始批量处理 {len(df)} 行数据")

            # 创建owner列（向量化操作）
            df['owner'] = df[package_column].map(package_owner_mapping).fillna('未知团队')

            # 添加文件来源和行索引
            df['file_source'] = file_path
            df['row_index'] = df.index

            # 重命名列以标准化输出
            result_df = df.rename(columns={
                package_column: 'package_name',
                causedby_column: 'causedby_info',
                path_column: 'path_info',
                version_column: 'version_info'
            })

            # 选择需要的列
            required_columns = ['package_name', 'owner', 'causedby_info', 'path_info', 'version_info', 'file_source', 'row_index']
            result_df = result_df[required_columns]

            # 转换为字典列表（保持原有接口兼容性）
            results = result_df.to_dict('records')

            # 统计信息
            unique_packages = result_df['package_name'].nunique()
            unique_owners = result_df['owner'].nunique()
            unknown_owners = len(result_df[result_df['owner'] == '未知团队'])

            self.logger.info(f"  分析完成:")
            self.logger.info(f"    总记录数: {len(results)}")
            self.logger.info(f"    唯一Package数: {unique_packages}")
            self.logger.info(f"    涉及团队数: {unique_owners}")
            self.logger.info(f"    未知团队记录: {unknown_owners}")

            # 显示Package分布统计
            if len(results) > 0:
                package_counts = result_df['package_name'].value_counts().head(5)
                self.logger.info(f"    Top 5 Package:")
                for pkg, count in package_counts.items():
                    self.logger.info(f"      {pkg}: {count} 条记录")

            # 记录Excel分析结果
            self.logger.log_excel_analysis(file_path, len(results), "成功")
            op_logger.success(f"分析完成: {len(results)} 条记录")
            return results

        except FileNotFoundError:
            error_msg = f"文件不存在 - {file_path}"
            self.logger.error(f"  {error_msg}")
            self.logger.log_excel_analysis(file_path, 0, "失败", "文件不存在")
            op_logger.failure("文件不存在")
            return []
        except pd.errors.EmptyDataError:
            error_msg = f"Excel文件为空或格式错误 - {file_path}"
            self.logger.error(f"  {error_msg}")
            self.logger.log_excel_analysis(file_path, 0, "失败", "文件为空或格式错误")
            op_logger.failure("文件为空或格式错误")
            return []
        except PermissionError:
            error_msg = f"没有权限访问文件 - {file_path}"
            self.logger.error(f"  {error_msg}")
            self.logger.log_excel_analysis(file_path, 0, "失败", "权限错误")
            op_logger.failure("权限错误")
            return []
        except (ValueError, OSError) as e:
            # 处理Excel文件格式错误，包括openpyxl的InvalidFileException等
            error_msg = str(e).lower()
            if ("excel" in error_msg or "openpyxl" in error_msg or "format" in error_msg or
                "zip file" in error_msg or "not a zip" in error_msg):
                self.logger.error(f"  无效的Excel文件格式 - {file_path}")
                self.logger.log_excel_analysis(file_path, 0, "失败", "无效文件格式")
                op_logger.failure("无效文件格式")
            else:
                self.logger.error(f"  文件读取失败 - {file_path}: {str(e)}")
                self.logger.log_excel_analysis(file_path, 0, "失败", f"读取失败: {str(e)}")
                op_logger.failure(f"读取失败: {str(e)}")
            return []
        except Exception as e:
            # 处理BadZipFile和其他Excel相关异常
            error_msg = str(e).lower()
            error_type = type(e).__name__
            if (error_type == 'BadZipFile' or "zip file" in error_msg or "not a zip" in error_msg or
                "excel" in error_msg or "openpyxl" in error_msg):
                self.logger.error(f"  无效的Excel文件格式 - {file_path}")
                self.logger.log_excel_analysis(file_path, 0, "失败", "无效文件格式")
                op_logger.failure("无效文件格式")
            else:
                self.logger.error(f"  分析Excel文件失败 - {file_path}")
                self.logger.error(f"    异常详情: {str(e)}")
                self.logger.exception(f"Excel分析异常: {file_path}")
                self.logger.log_excel_analysis(file_path, 0, "失败", f"分析异常: {str(e)}")
                op_logger.failure(f"分析异常: {str(e)}")
            return []

    def analyze_excel_files_batch(self, file_paths):
        """
        批量分析多个Excel文件（pandas优化版本）
        :param file_paths: Excel文件路径列表
        :return: 合并的分析结果
        """
        # 创建批量分析操作日志器
        op_logger = self.logger.create_operation_logger(f"批量Excel分析")

        self.logger.info(f"开始批量分析 {len(file_paths)} 个Excel文件")
        self.logger.info("=" * 60)
        op_logger.step(f"开始批量分析 {len(file_paths)} 个文件")

        all_results = []
        success_count = 0
        failed_count = 0

        # 预加载Package-Owner映射，避免重复加载
        package_owner_mapping = self.get_package_owner_mapping()
        op_logger.step("预加载Package-Owner映射")

        for i, file_path in enumerate(file_paths, 1):
            self.logger.info(f"\n[{i}/{len(file_paths)}] 处理文件: {os.path.basename(file_path)}")

            try:
                results = self.analyze_excel_file(file_path)
                if results:
                    all_results.extend(results)
                    success_count += 1
                    self.logger.info(f"  ✓ 成功分析，获得 {len(results)} 条记录")
                else:
                    failed_count += 1
                    self.logger.warning(f"  ✗ 分析失败或无有效数据")

            except Exception as e:
                failed_count += 1
                self.logger.error(f"  ✗ 分析异常: {str(e)}")
                self.logger.exception(f"批量分析异常: {file_path}")

        # 如果有结果，进行批量数据分析
        if all_results:
            self.logger.info(f"\n" + "=" * 60)
            self.logger.info("批量分析统计")
            self.logger.info("=" * 60)

            # 转换为DataFrame进行高效分析
            df_all = pd.DataFrame(all_results)

            # 统计信息
            total_records = len(df_all)
            unique_packages = df_all['package_name'].nunique()
            unique_owners = df_all['owner'].nunique()
            unique_files = df_all['file_source'].nunique()

            self.logger.info(f"处理结果:")
            self.logger.info(f"  成功文件: {success_count}")
            self.logger.info(f"  失败文件: {failed_count}")
            self.logger.info(f"  总记录数: {total_records}")
            self.logger.info(f"  唯一Package数: {unique_packages}")
            self.logger.info(f"  涉及团队数: {unique_owners}")
            self.logger.info(f"  处理文件数: {unique_files}")

            # Package分布统计
            self.logger.info(f"\nPackage分布 (Top 10):")
            package_counts = df_all['package_name'].value_counts().head(10)
            for pkg, count in package_counts.items():
                self.logger.info(f"  {pkg}: {count} 条记录")

            # Owner分布统计
            self.logger.info(f"\n团队分布:")
            owner_counts = df_all['owner'].value_counts()
            for owner, count in owner_counts.items():
                self.logger.info(f"  {owner}: {count} 条记录")

            # 文件分布统计
            self.logger.info(f"\n文件分布:")
            file_counts = df_all['file_source'].apply(os.path.basename).value_counts()
            for file_name, count in file_counts.items():
                self.logger.info(f"  {file_name}: {count} 条记录")

            # 记录批量操作结果
            self.logger.log_batch_operation("Excel分析", len(file_paths), success_count, failed_count)
            op_logger.success(f"批量分析完成: 成功 {success_count}/{len(file_paths)} 个文件，获得 {total_records} 条记录")

        else:
            self.logger.warning(f"批量分析完成，但没有获得有效数据")
            self.logger.info(f"  成功文件: {success_count}")
            self.logger.info(f"  失败文件: {failed_count}")
            op_logger.failure(f"批量分析无有效数据: 成功 {success_count}, 失败 {failed_count}")

        return all_results

    def export_analysis_results(self, results, output_path=None, target_date=None):
        """
        导出分析结果到Excel文件（pandas优化版本）
        :param results: 分析结果列表
        :param output_path: 输出文件路径，如果为None则自动生成
        :param target_date: 目标日期，用于文件命名
        :return: 导出的文件路径
        """
        if not results:
            self.logger.warning("没有数据可导出")
            return None

        # 创建导出操作日志器
        op_logger = self.logger.create_operation_logger("导出分析结果")

        # 获取目标日期
        if target_date is None:
            target_date = datetime.now().strftime('%Y-%m-%d')

        # 生成输出文件路径
        if output_path is None:
            date_suffix = target_date.replace('-', '')
            output_filename = f"aimonkey_analysis_results_{date_suffix}.xlsx"
            output_path = os.path.join(self.local_base_dir, output_filename)

        self.logger.info(f"开始导出分析结果到: {output_path}")
        op_logger.step(f"准备导出到: {os.path.basename(output_path)}")

        try:
            # 转换为DataFrame
            df = pd.DataFrame(results)
            op_logger.step(f"转换数据: {len(df)} 条记录")

            # 创建Excel写入器
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 主数据表
                df.to_excel(writer, sheet_name='分析结果', index=False)
                op_logger.step("写入主数据表")

                # 统计表
                stats_data = []

                # 基本统计
                stats_data.append(['总记录数', len(df)])
                stats_data.append(['唯一Package数', df['package_name'].nunique()])
                stats_data.append(['涉及团队数', df['owner'].nunique()])
                stats_data.append(['处理文件数', df['file_source'].nunique()])
                stats_data.append(['未知团队记录数', len(df[df['owner'] == '未知团队'])])

                stats_df = pd.DataFrame(stats_data, columns=['统计项', '数值'])
                stats_df.to_excel(writer, sheet_name='统计信息', index=False)
                op_logger.step("写入统计信息表")

                # Package分布表
                package_counts = df['package_name'].value_counts().reset_index()
                package_counts.columns = ['Package名称', '记录数']
                package_counts.to_excel(writer, sheet_name='Package分布', index=False)
                op_logger.step("写入Package分布表")

                # 团队分布表
                owner_counts = df['owner'].value_counts().reset_index()
                owner_counts.columns = ['团队名称', '记录数']
                owner_counts.to_excel(writer, sheet_name='团队分布', index=False)
                op_logger.step("写入团队分布表")

                # 文件分布表
                file_counts = df['file_source'].apply(os.path.basename).value_counts().reset_index()
                file_counts.columns = ['文件名', '记录数']
                file_counts.to_excel(writer, sheet_name='文件分布', index=False)
                op_logger.step("写入文件分布表")

            file_size = os.path.getsize(output_path)
            self.logger.info(f"✓ 成功导出分析结果")
            self.logger.info(f"  文件路径: {output_path}")
            self.logger.info(f"  文件大小: {self.format_file_size(file_size)}")
            self.logger.info(f"  包含工作表: 分析结果, 统计信息, Package分布, 团队分布, 文件分布")

            # 记录文件操作
            self.logger.log_file_operation("导出", output_path, "成功", f"大小: {self.format_file_size(file_size)}")
            op_logger.success(f"导出成功: {self.format_file_size(file_size)}")

            return output_path

        except Exception as e:
            self.logger.error(f"✗ 导出分析结果失败: {str(e)}")
            self.logger.exception(f"导出异常: {output_path}")
            op_logger.failure(f"导出失败: {str(e)}")
            return None

    def generate_summary_report(self, results, target_date=None):
        """
        生成分析摘要报告（pandas优化版本）
        :param results: 分析结果列表
        :param target_date: 目标日期
        :return: 摘要报告字典
        """
        if not results:
            self.logger.warning("没有数据可生成摘要报告")
            return None

        # 创建摘要报告操作日志器
        op_logger = self.logger.create_operation_logger("生成摘要报告")

        self.logger.info("生成分析摘要报告...")
        op_logger.step("开始生成摘要报告")

        # 转换为DataFrame进行分析
        df = pd.DataFrame(results)
        op_logger.step(f"分析 {len(df)} 条记录")

        # 基本统计
        summary = {
            'date': target_date or datetime.now().strftime('%Y-%m-%d'),
            'total_records': len(df),
            'unique_packages': df['package_name'].nunique(),
            'unique_owners': df['owner'].nunique(),
            'processed_files': df['file_source'].nunique(),
            'unknown_owners': len(df[df['owner'] == '未知团队']),
        }
        op_logger.step("计算基本统计信息")

        # Top Package
        top_packages = df['package_name'].value_counts().head(10).to_dict()
        summary['top_packages'] = top_packages
        op_logger.step("分析Top Package")

        # 团队分布
        owner_distribution = df['owner'].value_counts().to_dict()
        summary['owner_distribution'] = owner_distribution
        op_logger.step("分析团队分布")

        # 文件分布
        file_distribution = df['file_source'].apply(os.path.basename).value_counts().to_dict()
        summary['file_distribution'] = file_distribution
        op_logger.step("分析文件分布")

        # 异常关键词分析
        exception_keywords = ['crash', 'error', 'exception', 'fail', 'null', '崩溃', '错误', '异常', '失败']
        keyword_counts = {}

        for keyword in exception_keywords:
            count = df['causedby_info'].str.contains(keyword, case=False, na=False).sum()
            if count > 0:
                keyword_counts[keyword] = count

        summary['exception_keywords'] = keyword_counts
        op_logger.step("分析异常关键词")

        self.logger.info(f"✓ 摘要报告生成完成")
        self.logger.info(f"  分析日期: {summary['date']}")
        self.logger.info(f"  总记录数: {summary['total_records']}")
        self.logger.info(f"  涉及Package: {summary['unique_packages']}")
        self.logger.info(f"  涉及团队: {summary['unique_owners']}")

        op_logger.success(f"摘要报告生成完成: {summary['total_records']} 条记录")
        return summary
    
    def process_all_target_files(self, target_date=None):
        """
        处理所有目标Excel文件（pandas优化版本）
        :param target_date: 目标日期，格式为YYYY-MM-DD，如果为None则使用当前日期
        """
        # 创建目标文件处理操作日志器
        op_logger = self.logger.create_operation_logger("处理目标Excel文件")

        self.logger.info("=" * 60)
        self.logger.info("开始处理所有目标Excel文件")
        self.logger.info("=" * 60)

        # 获取目标日期
        if target_date is None:
            target_date = datetime.now().strftime('%Y-%m-%d')

        self.logger.info(f"目标日期: {target_date}")
        op_logger.step(f"设置目标日期: {target_date}")

        # 处理两个目录
        directories = [
            os.path.join(self.local_base_dir, 'tOS15.1', 'AIMonkey', target_date),
            os.path.join(self.local_base_dir, 'tOS16.0', 'AIMonkey', target_date)
        ]

        # 收集所有目标文件
        all_target_files = []

        for directory in directories:
            self.logger.info(f"\n扫描目录: {directory}")
            target_files = self.find_target_excel_files(directory, target_date)

            if target_files:
                self.logger.info(f"  找到 {len(target_files)} 个目标文件")
                for file_path in target_files:
                    self.logger.info(f"    - {os.path.basename(file_path)}")
                all_target_files.extend(target_files)
                op_logger.step(f"目录 {os.path.basename(directory)}: 找到 {len(target_files)} 个文件")
            else:
                self.logger.warning(f"  未找到目标文件")
                op_logger.step(f"目录 {os.path.basename(directory)}: 未找到文件", "warning")

        if not all_target_files:
            self.logger.warning(f"没有找到任何目标Excel文件")
            op_logger.failure("未找到任何目标文件")
            return []

        self.logger.info(f"\n总共找到 {len(all_target_files)} 个目标文件，开始批量分析...")
        op_logger.step(f"准备批量分析 {len(all_target_files)} 个文件")

        # 使用批量分析方法
        all_results = self.analyze_excel_files_batch(all_target_files)

        op_logger.success(f"目标文件处理完成: {len(all_results)} 条记录")
        return all_results
    
    def group_exceptions_by_owner(self, results):
        """
        按owner分组异常信息
        """
        grouped_results = {}
        
        for result in results:
            owner = result['owner']
            if owner not in grouped_results:
                grouped_results[owner] = []
            grouped_results[owner].append(result)
            
        return grouped_results
    
    def format_notification_message(self, owner, exceptions):
        """
        格式化通知消息（优化版本，使用Path、Version、CausedBy字段）
        """
        message = f"【AIMonkey异常通知】\n"
        message += f"负责团队: {owner}\n"
        message += f"异常数量: {len(exceptions)}\n"
        message += f"检测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        message += f"=" * 50 + "\n\n"

        for i, exception in enumerate(exceptions, 1):
            message += f"异常 #{i}\n"
            message += f"📱 Package: {exception.get('package_name', 'N/A')}\n"

            # 版本信息
            version_info = exception.get('version_info', 'N/A')
            if version_info and version_info != '未知版本' and version_info != 'N/A':
                message += f"🔢 Version: {version_info}\n"

            # 路径信息（简化显示）
            path_info = exception.get('path_info', 'N/A')
            if path_info and path_info != '未知路径' and path_info != 'N/A':
                # 提取关键路径信息
                if len(path_info) > 100:
                    # 如果路径太长，只显示关键部分
                    path_parts = path_info.split('\\')
                    if len(path_parts) > 3:
                        simplified_path = f"...\\{path_parts[-3]}\\{path_parts[-2]}\\{path_parts[-1]}"
                    else:
                        simplified_path = path_info[-100:]
                    message += f"📁 Path: {simplified_path}\n"
                else:
                    message += f"📁 Path: {path_info}\n"

            # 异常原因
            causedby_info = exception.get('causedby_info', 'N/A')
            if causedby_info and causedby_info != '未知异常' and causedby_info != 'N/A':
                # 提取异常的关键信息
                if len(causedby_info) > 200:
                    # 尝试提取异常类型和关键信息
                    lines = causedby_info.split('\n')
                    key_info = []
                    for line in lines[:5]:  # 只取前5行
                        line = line.strip()
                        if line and ('Exception' in line or 'Error' in line or 'at ' in line):
                            key_info.append(line)

                    if key_info:
                        message += f"⚠️  CausedBy: {key_info[0]}\n"
                        if len(key_info) > 1:
                            message += f"         {key_info[1]}\n"
                    else:
                        message += f"⚠️  CausedBy: {causedby_info[:150]}...\n"
                else:
                    message += f"⚠️  CausedBy: {causedby_info}\n"

            # 来源文件
            file_source = exception.get('file_source', 'N/A')
            if file_source:
                message += f"📄 Source: {os.path.basename(file_source)}\n"

            message += f"-" * 30 + "\n\n"

        message += f"请及时处理相关异常问题。\n"
        message += f"如有疑问，请联系AIMonkey团队。"

        return message
    
    def send_notifications(self, grouped_results):
        """
        发送飞书通知（pandas优化版本）
        :param grouped_results: 按owner分组的异常结果
        :return: 成功发送的通知数量
        """
        if not grouped_results:
            self.logger.warning("没有需要发送的通知")
            return 0

        # 创建通知操作日志器
        op_logger = self.logger.create_operation_logger("发送飞书通知")

        self.logger.info(f"准备发送通知给 {len(grouped_results)} 个团队")
        op_logger.step(f"准备发送 {len(grouped_results)} 个通知")

        sent_count = 0
        failed_count = 0

        for owner, exceptions in grouped_results.items():
            if owner == '未知团队':
                self.logger.info(f"跳过未知团队 ({len(exceptions)} 条记录)")
                continue

            self.logger.info(f"发送通知给: {owner} ({len(exceptions)} 条异常)")
            op_logger.step(f"发送通知给: {owner}")

            try:
                message = self.format_notification_message(owner, exceptions)

                # 使用飞书通知
                response = self.feishu_bot.send_bug_notification_v6(
                    assignee=owner,
                    bug_data=json.dumps({"text": message}),
                    notification_type="AIMonkey异常信息"
                )

                sent_count += 1
                self.logger.info(f"  ✓ 通知发送成功: {response}")
                op_logger.step(f"通知发送成功: {owner}")

            except Exception as e:
                failed_count += 1
                self.logger.error(f"  ✗ 通知发送失败: {str(e)}")
                self.logger.exception(f"通知发送异常: {owner}")
                op_logger.step(f"通知发送失败: {owner} - {str(e)}", "error")

        self.logger.info(f"\n通知发送统计:")
        self.logger.info(f"  成功发送: {sent_count} 条")
        self.logger.info(f"  发送失败: {failed_count} 条")

        # 记录批量操作结果
        self.logger.log_batch_operation("通知发送", len(grouped_results), sent_count, failed_count)
        op_logger.success(f"通知发送完成: 成功 {sent_count}/{len(grouped_results)} 条")

        return sent_count
    
    def run_full_process(self, target_date=None, export_results=True, enable_notifications=True):
        """
        执行完整的处理流程（优化版本）
        :param target_date: 目标日期，格式为YYYY-MM-DD，如果为None则使用当前日期
        :param export_results: 是否导出分析结果到Excel文件
        :param enable_notifications: 是否启用飞书通知
        :return: 处理结果字典
        """
        # 获取目标日期
        if target_date is None:
            target_date = datetime.now().strftime('%Y-%m-%d')

        # 创建主流程操作日志器
        main_op_logger = self.logger.create_operation_logger(f"AIMonkey处理流程: {target_date}")

        self.logger.info("=" * 80)
        self.logger.info(f"开始AIMonkey异常信息处理流程 (目标日期: {target_date})")
        self.logger.info("=" * 80)

        process_result = {
            'success': False,
            'target_date': target_date,
            'downloaded_files': [],
            'found_files': [],
            'analysis_results': [],
            'grouped_results': {},
            'summary_report': None,
            'export_file': None,
            'notifications_sent': 0,
            'processing_stats': {
                'total_files_found': 0,
                'files_analyzed': 0,
                'total_records': 0,
                'unique_packages': 0,
                'teams_involved': 0
            }
        }

        try:
            # 步骤1: 下载文件
            self.logger.info("\n步骤1: 下载AIMonkey文件...")
            main_op_logger.step("开始下载文件")

            downloaded_files = self.download_aimonkey_files(target_date)
            process_result['downloaded_files'] = downloaded_files

            if not downloaded_files:
                self.logger.warning("文件下载失败或无文件可下载")
                main_op_logger.step("文件下载失败，尝试本地搜索", "warning")

                # 如果下载失败，尝试在本地目录搜索现有文件
                self.logger.info("尝试在本地目录搜索现有文件...")
                local_directories = [
                    os.path.join(self.local_base_dir, 'tOS15.1', 'AIMonkey', target_date),
                    os.path.join(self.local_base_dir, 'tOS16.0', 'AIMonkey', target_date),
                    os.path.join(self.local_base_dir, 'tOS15.1', 'AIMonkey'),
                    os.path.join(self.local_base_dir, 'tOS16.0', 'AIMonkey'),
                ]

                found_files = []
                for directory in local_directories:
                    if os.path.exists(directory):
                        # 使用优化的文件搜索方法
                        files = self.find_target_excel_files(
                            directory=directory,
                            target_date=target_date,
                            pattern_keywords=['MonkeyAEE', 'Result', 'AIMonkey']
                        )
                        found_files.extend(files)

                if found_files:
                    self.logger.info(f"在本地找到 {len(found_files)} 个相关文件")
                    process_result['found_files'] = found_files
                    main_op_logger.step(f"本地搜索找到 {len(found_files)} 个文件")
                else:
                    self.logger.error("本地也未找到相关文件，流程终止")
                    main_op_logger.failure("未找到任何相关文件")
                    return process_result
            else:
                # 验证下载的文件
                main_op_logger.step("验证下载文件")
                verification_results = self.verify_downloaded_files(downloaded_files)
                if verification_results['valid'] == 0:
                    self.logger.error("没有有效的下载文件，流程终止")
                    main_op_logger.failure("没有有效的下载文件")
                    return process_result

                process_result['found_files'] = downloaded_files

            # 步骤2: 智能搜索和分析Excel文件
            self.logger.info("\n步骤2: 智能搜索和分析Excel文件...")
            main_op_logger.step("开始智能文件搜索和分析")

            # 使用优化的文件搜索方法
            all_target_files = []

            # 如果有下载的文件，直接分析
            if process_result['found_files']:
                all_target_files = process_result['found_files']

            # 额外搜索特定模式的文件
            search_directories = [
                os.path.join(self.local_base_dir, 'tOS15.1', 'AIMonkey'),
                os.path.join(self.local_base_dir, 'tOS16.0', 'AIMonkey'),
            ]

            for directory in search_directories:
                if os.path.exists(directory):
                    # 使用多种搜索方法

                    # 方法1: 标准目标文件搜索
                    target_files = self.find_target_excel_files(
                        directory=directory,
                        target_date=target_date,
                        pattern_keywords=['MonkeyAEE', 'Result', 'AIMonkey']
                    )

                    # 方法2: 关键词搜索
                    keyword_files = self.search_files_by_keywords(
                        directory=directory,
                        keywords=['Result', 'MonkeyAEE', 'SH'],
                        file_extensions=['.xls', '.xlsx'],
                        target_date=target_date
                    )

                    # 合并结果并去重
                    combined_files = list(set(target_files + keyword_files))
                    all_target_files.extend(combined_files)

            # 去重
            all_target_files = list(set(all_target_files))
            process_result['processing_stats']['total_files_found'] = len(all_target_files)

            if not all_target_files:
                self.logger.warning("未找到任何目标Excel文件")
                process_result['success'] = True  # 下载成功但无分析文件
                main_op_logger.success("处理完成: 未找到目标Excel文件")
                return process_result

            self.logger.info(f"找到 {len(all_target_files)} 个目标Excel文件，开始分析...")

            # 批量分析Excel文件（使用优化的分析方法）
            results = self.analyze_excel_files_batch(all_target_files)

            process_result['analysis_results'] = results
            process_result['processing_stats']['files_analyzed'] = len([f for f in all_target_files if f])

            if results:
                process_result['processing_stats']['total_records'] = len(results)

                # 计算统计信息
                df_results = pd.DataFrame(results)
                process_result['processing_stats']['unique_packages'] = df_results['package_name'].nunique()
                process_result['processing_stats']['teams_involved'] = df_results['owner'].nunique()

                self.logger.info(f"分析完成，获得 {len(results)} 条异常记录")
                main_op_logger.step(f"分析完成: {len(results)} 条记录")
            else:
                self.logger.warning("分析完成，但未获得有效异常记录")
                main_op_logger.step("分析完成，但无有效记录", "warning")

            # 步骤3: 生成摘要报告
            if results:
                self.logger.info("\n步骤3: 生成摘要报告...")
                main_op_logger.step("生成摘要报告")
                summary_report = self.generate_summary_report(results, target_date)
                process_result['summary_report'] = summary_report

                if summary_report:
                    self.logger.info("摘要报告生成成功")
                    main_op_logger.step("摘要报告生成成功")

            # 步骤4: 导出分析结果（可选）
            if export_results and results:
                self.logger.info("\n步骤4: 导出分析结果...")
                main_op_logger.step("导出分析结果")
                export_file = self.export_analysis_results(results, target_date=target_date)
                process_result['export_file'] = export_file

                if export_file:
                    self.logger.info(f"分析结果已导出到: {os.path.basename(export_file)}")
                    main_op_logger.step(f"导出成功: {os.path.basename(export_file)}")

            # 步骤5: 按owner分组异常信息
            if results:
                self.logger.info("\n步骤5: 按owner分组异常信息...")
                main_op_logger.step("按owner分组异常信息")
                grouped_results = self.group_exceptions_by_owner(results)
                process_result['grouped_results'] = grouped_results

                self.logger.info(f"异常信息已按 {len(grouped_results)} 个团队分组")
                main_op_logger.step(f"分组完成: {len(grouped_results)} 个团队")

            # 步骤6: 发送飞书通知（可选）
            if enable_notifications and results and grouped_results:
                self.logger.info("\n步骤6: 发送飞书通知...")
                main_op_logger.step("发送飞书通知")

                try:
                    notifications_sent = self.send_notifications(grouped_results)
                    process_result['notifications_sent'] = notifications_sent

                    if notifications_sent > 0:
                        self.logger.info(f"成功发送 {notifications_sent} 条通知")
                        main_op_logger.step(f"通知发送成功: {notifications_sent} 条")
                    else:
                        self.logger.warning("未发送任何通知")
                        main_op_logger.step("未发送任何通知", "warning")

                except Exception as e:
                    self.logger.error(f"发送通知失败: {str(e)}")
                    main_op_logger.step(f"通知发送失败: {str(e)}", "error")
                    # 通知失败不影响整体流程成功
            elif not enable_notifications:
                self.logger.info("\n步骤6: 跳过飞书通知（已禁用）")
                main_op_logger.step("跳过飞书通知")

            # 标记流程成功
            process_result['success'] = True

            # 输出最终统计
            self.logger.info("\n" + "=" * 80)
            self.logger.info("处理流程完成统计")
            self.logger.info("=" * 80)
            self.logger.info(f"✓ 目标日期: {target_date}")
            self.logger.info(f"✓ 找到文件: {process_result['processing_stats']['total_files_found']} 个")
            self.logger.info(f"✓ 分析文件: {process_result['processing_stats']['files_analyzed']} 个")
            self.logger.info(f"✓ 分析记录: {process_result['processing_stats']['total_records']} 条")
            self.logger.info(f"✓ 涉及Package: {process_result['processing_stats']['unique_packages']} 个")
            self.logger.info(f"✓ 涉及团队: {process_result['processing_stats']['teams_involved']} 个")
            self.logger.info(f"✓ 发送通知: {process_result['notifications_sent']} 条")

            if export_results and process_result['export_file']:
                self.logger.info(f"✓ 导出文件: {os.path.basename(process_result['export_file'])}")

            self.logger.info("=" * 80)
            self.logger.info("AIMonkey异常信息处理流程完成")
            self.logger.info("=" * 80)

            main_op_logger.success(f"处理流程完成: {process_result['processing_stats']['total_records']} 条记录, {process_result['notifications_sent']} 条通知")
            return process_result

        except Exception as e:
            self.logger.error(f"处理流程异常: {str(e)}")
            self.logger.exception("处理流程异常")
            main_op_logger.failure(f"处理流程异常: {str(e)}")
            return process_result


    def run_for_date(self, year, month, day):
        """
        为指定日期运行处理流程
        :param year: 年份
        :param month: 月份
        :param day: 日期
        """
        target_date = f"{year:04d}-{month:02d}-{day:02d}"
        return self.run_full_process(target_date)

    def run_for_today(self):
        """
        为今天运行处理流程
        """
        return self.run_full_process()

    def run_for_yesterday(self):
        """
        为昨天运行处理流程
        """
        from datetime import timedelta
        yesterday = datetime.now() - timedelta(days=1)
        target_date = yesterday.strftime('%Y-%m-%d')
        return self.run_full_process(target_date)

    def run_batch_process(self, start_date, end_date):
        """
        批量处理指定日期范围的数据
        :param start_date: 开始日期，格式为YYYY-MM-DD
        :param end_date: 结束日期，格式为YYYY-MM-DD
        """
        from datetime import timedelta

        # 创建批量处理操作日志器
        batch_op_logger = self.logger.create_operation_logger(f"批量处理: {start_date} 到 {end_date}")

        start = datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.strptime(end_date, '%Y-%m-%d')

        current_date = start
        results = []
        total_days = (end - start).days + 1

        self.logger.info(f"开始批量处理: {start_date} 到 {end_date} (共 {total_days} 天)")
        batch_op_logger.step(f"开始批量处理 {total_days} 天的数据")

        while current_date <= end:
            date_str = current_date.strftime('%Y-%m-%d')
            self.logger.info(f"\n{'='*50}")
            self.logger.info(f"处理日期: {date_str}")
            self.logger.info(f"{'='*50}")

            success = self.run_full_process(date_str)
            results.append({
                'date': date_str,
                'success': success
            })

            batch_op_logger.step(f"处理日期 {date_str}: {'成功' if success else '失败'}")
            current_date += timedelta(days=1)

        # 输出批量处理结果
        self.logger.info(f"\n{'='*50}")
        self.logger.info("批量处理结果汇总:")
        self.logger.info(f"{'='*50}")

        success_count = sum(1 for r in results if r['success'])
        failed_count = len(results) - success_count

        for result in results:
            status = "成功" if result['success'] else "失败"
            self.logger.info(f"{result['date']}: {status}")

        self.logger.log_batch_operation("日期处理", len(results), success_count, failed_count)
        batch_op_logger.success(f"批量处理完成: 成功 {success_count}/{len(results)} 天")

        return results


if __name__ == '__main__':
    # 使用示例
    aimonkey_tool = AIMonkeyTools()

    # 示例1: 处理今天的数据
    # aimonkey_tool.run_for_today()

    # 示例2: 处理指定日期的数据
    aimonkey_tool.run_for_date(2025, 7, 28)

    # 示例3: 处理昨天的数据
    # aimonkey_tool.run_for_yesterday()

    # 示例4: 批量处理多天的数据
    # aimonkey_tool.run_batch_process('2025-07-12', '2025-07-14')

    # 默认运行今天的数据
    # aimonkey_tool.run_for_today()
