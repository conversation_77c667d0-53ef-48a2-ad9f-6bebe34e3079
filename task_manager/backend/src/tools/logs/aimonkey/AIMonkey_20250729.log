2025-07-29 17:45:27 - INFO - AIMonkey工具初始化完成
2025-07-29 17:45:27 - INFO - 重试配置: {'max_retries': 1, 'retry_delay': 2}
2025-07-29 17:45:27 - INFO - 开始操作: AIMonkey处理流程: 2025-07-29
2025-07-29 17:45:27 - INFO - ================================================================================
2025-07-29 17:45:27 - INFO - 开始AIMonkey异常信息处理流程 (目标日期: 2025-07-29)
2025-07-29 17:45:27 - INFO - ================================================================================
2025-07-29 17:45:27 - INFO - 
步骤1: 下载AIMonkey文件...
2025-07-29 17:45:27 - INFO - [AIMonkey处理流程: 2025-07-29] 步骤 1: 开始下载文件
2025-07-29 17:45:27 - INFO - 开始操作: AIMonkey文件下载
2025-07-29 17:45:27 - INFO - ============================================================
2025-07-29 17:45:27 - INFO - 开始下载AIMonkey文件
2025-07-29 17:45:27 - INFO - ============================================================
2025-07-29 17:45:27 - INFO - SMB会话已建立: 10.205.101.200
2025-07-29 17:45:27 - INFO - 目标日期: 2025-07-29
2025-07-29 17:45:27 - INFO - 本地基础目录: D:\Monkey
2025-07-29 17:45:27 - INFO - [AIMonkey文件下载] 步骤 1: 设置目标日期: 2025-07-29
2025-07-29 17:45:27 - INFO - 
[1/2] 处理目录: tOS15.1
2025-07-29 17:45:27 - INFO - 远程路径: tOS15.1\AIMonkey\2025-07-29
2025-07-29 17:45:27 - INFO - 本地路径: D:\Monkey\tOS15.1\AIMonkey\2025-07-29
2025-07-29 17:45:27 - INFO - [AIMonkey文件下载] 步骤 2: 处理目录 tOS15.1
2025-07-29 17:45:27 - INFO - 开始操作: SMB文件下载: tOS15.1\AIMonkey\2025-07-29
2025-07-29 17:45:27 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-29] 步骤 1: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-07-29
2025-07-29 17:45:27 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-29] 步骤 2: 连接远程目录: \\10.205.101.200\osptlog1\tOS15.1\AIMonkey\2025-07-29
2025-07-29 17:45:27 - INFO - 找到 10 个文件/目录
2025-07-29 17:45:27 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-29] 步骤 3: 扫描到 10 个文件/目录
2025-07-29 17:45:27 - INFO - file_name: 基础服务
2025-07-29 17:45:27 - INFO - file_name: 桌面
2025-07-29 17:45:27 - INFO - file_name: 系统应用
2025-07-29 17:45:27 - INFO - file_name: 门户
2025-07-29 17:45:27 - INFO - file_name: 框架
2025-07-29 17:45:27 - INFO - file_name: 敏捷
2025-07-29 17:45:27 - INFO - file_name: 创新产品
2025-07-29 17:45:27 - INFO - file_name: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xls
2025-07-29 17:45:27 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-29] 步骤 4: 准备下载到本地: D:\Monkey\tOS15.1\AIMonkey\2025-07-29\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx
2025-07-29 17:45:27 - INFO - 开始操作: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xls
2025-07-29 17:45:27 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xls] 步骤 1: 开始下载文件
2025-07-29 17:45:27 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-07-29
2025-07-29 17:45:27 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xls] 步骤 3: 获取文件大小: 432.5 KB
2025-07-29 17:45:27 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xls] 步骤 4: 开始文件传输
2025-07-29 17:45:28 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xls] 步骤 5: 文件传输完成
2025-07-29 17:45:28 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xls] 步骤 6: 下载完成: 432.5 KB
2025-07-29 17:45:28 - INFO - 文件操作: 下载 - D:\Monkey\tOS15.1\AIMonkey\2025-07-29\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx - 成功 - 大小: 432.5 KB
2025-07-29 17:45:28 - INFO - 操作完成: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xls - 文件下载成功: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xls (耗时: 0.674s, 步骤数: 6)
2025-07-29 17:45:28 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-29] 步骤 5: 成功下载: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xls
2025-07-29 17:45:28 - INFO - file_name: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xls
2025-07-29 17:45:28 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-29] 步骤 6: 准备下载到本地: D:\Monkey\tOS15.1\AIMonkey\2025-07-29\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx
2025-07-29 17:45:28 - INFO - 开始操作: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xls
2025-07-29 17:45:28 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xls] 步骤 1: 开始下载文件
2025-07-29 17:45:28 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-07-29
2025-07-29 17:45:28 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xls] 步骤 3: 获取文件大小: 17.5 KB
2025-07-29 17:45:28 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xls] 步骤 4: 开始文件传输
2025-07-29 17:45:28 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xls] 步骤 5: 文件传输完成
2025-07-29 17:45:28 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xls] 步骤 6: 下载完成: 17.5 KB
2025-07-29 17:45:28 - INFO - 文件操作: 下载 - D:\Monkey\tOS15.1\AIMonkey\2025-07-29\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx - 成功 - 大小: 17.5 KB
2025-07-29 17:45:28 - INFO - 操作完成: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xls - 文件下载成功: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xls (耗时: 0.122s, 步骤数: 6)
2025-07-29 17:45:28 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-29] 步骤 7: 成功下载: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xls
2025-07-29 17:45:28 - INFO - file_name: statistics.txt
2025-07-29 17:45:28 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-29] 步骤 8: 准备下载到本地: D:\Monkey\tOS15.1\AIMonkey\2025-07-29\statistics.txt
2025-07-29 17:45:28 - INFO - 开始操作: 下载文件: statistics.txt
2025-07-29 17:45:28 - INFO - [下载文件: statistics.txt] 步骤 1: 开始下载文件
2025-07-29 17:45:28 - INFO - [下载文件: statistics.txt] 步骤 2: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-07-29
2025-07-29 17:45:28 - INFO - [下载文件: statistics.txt] 步骤 3: 获取文件大小: 354.0 B
2025-07-29 17:45:28 - INFO - [下载文件: statistics.txt] 步骤 4: 开始文件传输
2025-07-29 17:45:28 - INFO - [下载文件: statistics.txt] 步骤 5: 文件传输完成
2025-07-29 17:45:28 - INFO - [下载文件: statistics.txt] 步骤 6: 下载完成: 354.0 B
2025-07-29 17:45:28 - INFO - 文件操作: 下载 - D:\Monkey\tOS15.1\AIMonkey\2025-07-29\statistics.txt - 成功 - 大小: 354.0 B
2025-07-29 17:45:28 - INFO - 操作完成: 下载文件: statistics.txt - 文件下载成功: statistics.txt (耗时: 0.144s, 步骤数: 6)
2025-07-29 17:45:28 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-29] 步骤 9: 成功下载: statistics.txt
2025-07-29 17:45:28 - INFO - 操作完成: SMB文件下载: tOS15.1\AIMonkey\2025-07-29 - 下载完成，成功下载 3 个文件 (耗时: 1.266s, 步骤数: 9)
2025-07-29 17:45:28 - INFO - ✓ 成功下载 3 个文件
2025-07-29 17:45:28 - INFO - [AIMonkey文件下载] 步骤 3: 成功下载 3 个文件
2025-07-29 17:45:28 - INFO - 
[2/2] 处理目录: tOS16.0
2025-07-29 17:45:28 - INFO - 远程路径: tOS16.0\AIMonkey\2025-07-29
2025-07-29 17:45:28 - INFO - 本地路径: D:\Monkey\tOS16.0\AIMonkey\2025-07-29
2025-07-29 17:45:28 - INFO - [AIMonkey文件下载] 步骤 4: 处理目录 tOS16.0
2025-07-29 17:45:28 - INFO - 开始操作: SMB文件下载: tOS16.0\AIMonkey\2025-07-29
2025-07-29 17:45:28 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-29] 步骤 1: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-07-29
2025-07-29 17:45:28 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-29] 步骤 2: 连接远程目录: \\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-29
2025-07-29 17:45:28 - INFO - 找到 10 个文件/目录
2025-07-29 17:45:28 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-29] 步骤 3: 扫描到 10 个文件/目录
2025-07-29 17:45:28 - INFO - file_name: 基础服务
2025-07-29 17:45:28 - INFO - file_name: 创新产品
2025-07-29 17:45:28 - INFO - file_name: 桌面
2025-07-29 17:45:28 - INFO - file_name: 系统应用
2025-07-29 17:45:28 - INFO - file_name: 框架
2025-07-29 17:45:28 - INFO - file_name: 门户
2025-07-29 17:45:28 - INFO - file_name: 敏捷
2025-07-29 17:45:28 - INFO - file_name: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xls
2025-07-29 17:45:28 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-29] 步骤 4: 准备下载到本地: D:\Monkey\tOS16.0\AIMonkey\2025-07-29\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx
2025-07-29 17:45:28 - INFO - 开始操作: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xls
2025-07-29 17:45:28 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xls] 步骤 1: 开始下载文件
2025-07-29 17:45:28 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-07-29
2025-07-29 17:45:28 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xls] 步骤 3: 获取文件大小: 457.0 KB
2025-07-29 17:45:28 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xls] 步骤 4: 开始文件传输
2025-07-29 17:45:29 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xls] 步骤 5: 文件传输完成
2025-07-29 17:45:29 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xls] 步骤 6: 下载完成: 457.0 KB
2025-07-29 17:45:29 - INFO - 文件操作: 下载 - D:\Monkey\tOS16.0\AIMonkey\2025-07-29\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx - 成功 - 大小: 457.0 KB
2025-07-29 17:45:29 - INFO - 操作完成: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xls - 文件下载成功: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xls (耗时: 0.479s, 步骤数: 6)
2025-07-29 17:45:29 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-29] 步骤 5: 成功下载: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xls
2025-07-29 17:45:29 - INFO - file_name: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xls
2025-07-29 17:45:29 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-29] 步骤 6: 准备下载到本地: D:\Monkey\tOS16.0\AIMonkey\2025-07-29\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx
2025-07-29 17:45:29 - INFO - 开始操作: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xls
2025-07-29 17:45:29 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xls] 步骤 1: 开始下载文件
2025-07-29 17:45:29 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-07-29
2025-07-29 17:45:29 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xls] 步骤 3: 获取文件大小: 182.5 KB
2025-07-29 17:45:29 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xls] 步骤 4: 开始文件传输
2025-07-29 17:45:29 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xls] 步骤 5: 文件传输完成
2025-07-29 17:45:29 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xls] 步骤 6: 下载完成: 182.5 KB
2025-07-29 17:45:29 - INFO - 文件操作: 下载 - D:\Monkey\tOS16.0\AIMonkey\2025-07-29\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx - 成功 - 大小: 182.5 KB
2025-07-29 17:45:29 - INFO - 操作完成: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xls - 文件下载成功: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xls (耗时: 0.258s, 步骤数: 6)
2025-07-29 17:45:29 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-29] 步骤 7: 成功下载: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xls
2025-07-29 17:45:29 - INFO - file_name: statistics.txt
2025-07-29 17:45:29 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-29] 步骤 8: 准备下载到本地: D:\Monkey\tOS16.0\AIMonkey\2025-07-29\statistics.txt
2025-07-29 17:45:29 - INFO - 开始操作: 下载文件: statistics.txt
2025-07-29 17:45:29 - INFO - [下载文件: statistics.txt] 步骤 1: 开始下载文件
2025-07-29 17:45:29 - INFO - [下载文件: statistics.txt] 步骤 2: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-07-29
2025-07-29 17:45:29 - INFO - [下载文件: statistics.txt] 步骤 3: 获取文件大小: 370.0 B
2025-07-29 17:45:29 - INFO - [下载文件: statistics.txt] 步骤 4: 开始文件传输
2025-07-29 17:45:29 - INFO - [下载文件: statistics.txt] 步骤 5: 文件传输完成
2025-07-29 17:45:29 - INFO - [下载文件: statistics.txt] 步骤 6: 下载完成: 370.0 B
2025-07-29 17:45:29 - INFO - 文件操作: 下载 - D:\Monkey\tOS16.0\AIMonkey\2025-07-29\statistics.txt - 成功 - 大小: 370.0 B
2025-07-29 17:45:29 - INFO - 操作完成: 下载文件: statistics.txt - 文件下载成功: statistics.txt (耗时: 0.123s, 步骤数: 6)
2025-07-29 17:45:29 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-29] 步骤 9: 成功下载: statistics.txt
2025-07-29 17:45:29 - INFO - 操作完成: SMB文件下载: tOS16.0\AIMonkey\2025-07-29 - 下载完成，成功下载 3 个文件 (耗时: 1.037s, 步骤数: 9)
2025-07-29 17:45:29 - INFO - ✓ 成功下载 3 个文件
2025-07-29 17:45:29 - INFO - [AIMonkey文件下载] 步骤 5: 成功下载 3 个文件
2025-07-29 17:45:29 - INFO - 批量文件下载: 总计 2, 成功 6, 失败 0
2025-07-29 17:45:29 - INFO - 
============================================================
2025-07-29 17:45:29 - INFO - 下载完成统计
2025-07-29 17:45:29 - INFO - ============================================================
2025-07-29 17:45:29 - INFO - 总共下载: 6 个文件
2025-07-29 17:45:29 - INFO - 下载失败: 0 个文件
2025-07-29 17:45:29 - INFO - 目标目录数: 2
2025-07-29 17:45:29 - INFO - 
下载的文件列表:
2025-07-29 17:45:29 - INFO -   - Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx (432.5 KB)
2025-07-29 17:45:29 - INFO -   - Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx (17.5 KB)
2025-07-29 17:45:29 - INFO -   - statistics.txt (354.0 B)
2025-07-29 17:45:29 - INFO -   - Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx (457.0 KB)
2025-07-29 17:45:29 - INFO -   - Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx (182.5 KB)
2025-07-29 17:45:29 - INFO -   - statistics.txt (370.0 B)
2025-07-29 17:45:29 - INFO - 操作完成: AIMonkey文件下载 - 下载完成，总计 6 个文件 (耗时: 2.530s, 步骤数: 5)
2025-07-29 17:45:29 - INFO - [AIMonkey处理流程: 2025-07-29] 步骤 2: 验证下载文件
2025-07-29 17:45:29 - INFO - 开始操作: 文件验证
2025-07-29 17:45:29 - INFO - [文件验证] 步骤 1: 开始验证 6 个文件
2025-07-29 17:45:29 - INFO - ✓ Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx - 432.5 KB
2025-07-29 17:45:29 - INFO - ✓ Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx - 17.5 KB
2025-07-29 17:45:29 - INFO - ✓ statistics.txt - 354.0 B
2025-07-29 17:45:29 - INFO - ✓ Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx - 457.0 KB
2025-07-29 17:45:29 - INFO - ✓ Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx - 182.5 KB
2025-07-29 17:45:29 - INFO - ✓ statistics.txt - 370.0 B
2025-07-29 17:45:29 - INFO - 
验证结果:
2025-07-29 17:45:29 - INFO -   总文件数: 6
2025-07-29 17:45:29 - INFO -   有效文件: 6
2025-07-29 17:45:29 - INFO -   无效文件: 0
2025-07-29 17:45:29 - INFO -   缺失文件: 0
2025-07-29 17:45:29 - INFO - 操作完成: 文件验证 - 验证完成: 有效 6/6 个文件 (耗时: 0.009s, 步骤数: 1)
2025-07-29 17:45:29 - INFO - 
步骤2: 智能搜索和分析Excel文件...
2025-07-29 17:45:29 - INFO - [AIMonkey处理流程: 2025-07-29] 步骤 3: 开始智能文件搜索和分析
2025-07-29 17:45:29 - INFO - 开始操作: 搜索Excel文件: AIMonkey
2025-07-29 17:45:29 - INFO - [搜索Excel文件: AIMonkey] 步骤 1: 搜索日期后缀: 20250729
2025-07-29 17:45:29 - INFO - 搜索条件:
2025-07-29 17:45:29 - INFO -   目录: D:\Monkey\tOS15.1\AIMonkey
2025-07-29 17:45:29 - INFO -   日期后缀: 20250729
2025-07-29 17:45:29 - INFO -   关键词: ['MonkeyAEE', 'Result', 'AIMonkey']
2025-07-29 17:45:29 - INFO - [搜索Excel文件: AIMonkey] 步骤 2: 设置搜索条件: 日期=20250729, 关键词=['MonkeyAEE', 'Result', 'AIMonkey']
2025-07-29 17:45:29 - INFO - ✓ 找到匹配文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx
2025-07-29 17:45:29 - INFO - ✓ 找到匹配文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx
2025-07-29 17:45:29 - INFO - 在 D:\Monkey\tOS15.1\AIMonkey 中找到 2 个匹配的Excel文件
2025-07-29 17:45:29 - INFO - 找到的文件列表:
2025-07-29 17:45:29 - INFO -   1. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx (17.5 KB)
2025-07-29 17:45:29 - INFO -   2. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx (432.5 KB)
2025-07-29 17:45:29 - INFO - 操作完成: 搜索Excel文件: AIMonkey - 找到 2 个匹配文件 (耗时: 0.011s, 步骤数: 2)
2025-07-29 17:45:29 - INFO - 开始操作: 关键词搜索: Result, MonkeyAEE, SH
2025-07-29 17:45:29 - INFO - 关键词搜索配置:
2025-07-29 17:45:29 - INFO -   目录: D:\Monkey\tOS15.1\AIMonkey
2025-07-29 17:45:29 - INFO -   关键词: ['Result', 'MonkeyAEE', 'SH']
2025-07-29 17:45:29 - INFO -   扩展名: ['.xls', '.xlsx']
2025-07-29 17:45:29 - INFO -   日期模式: ['20250729', '2025_07_29', '2025.07.29']
2025-07-29 17:45:29 - INFO - [关键词搜索: Result, MonkeyAEE, SH] 步骤 1: 搜索关键词: ['Result', 'MonkeyAEE', 'SH'], 扩展名: ['.xls', '.xlsx']
2025-07-29 17:45:29 - INFO - 在 D:\Monkey\tOS15.1\AIMonkey 中找到 4 个匹配的文件
2025-07-29 17:45:29 - INFO - 找到的文件（按匹配度排序）:
2025-07-29 17:45:29 - INFO -   1. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx (分数: 4)
2025-07-29 17:45:29 - INFO -      大小: 17.5 KB
2025-07-29 17:45:29 - INFO -      匹配: 关键词: Result, 关键词: SH, 日期: 20250729
2025-07-29 17:45:29 - INFO -   2. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx (分数: 4)
2025-07-29 17:45:29 - INFO -      大小: 432.5 KB
2025-07-29 17:45:29 - INFO -      匹配: 关键词: Result, 关键词: SH, 日期: 20250729
2025-07-29 17:45:29 - INFO -   3. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls (分数: 2)
2025-07-29 17:45:29 - INFO -      大小: 41.5 KB
2025-07-29 17:45:29 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:45:29 - INFO -   4. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls (分数: 2)
2025-07-29 17:45:29 - INFO -      大小: 150.5 KB
2025-07-29 17:45:29 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:45:29 - INFO - 操作完成: 关键词搜索: Result, MonkeyAEE, SH - 找到 4 个匹配文件 (耗时: 0.009s, 步骤数: 1)
2025-07-29 17:45:29 - INFO - 开始操作: 搜索Excel文件: AIMonkey
2025-07-29 17:45:29 - INFO - [搜索Excel文件: AIMonkey] 步骤 1: 搜索日期后缀: 20250729
2025-07-29 17:45:29 - INFO - 搜索条件:
2025-07-29 17:45:29 - INFO -   目录: D:\Monkey\tOS16.0\AIMonkey
2025-07-29 17:45:29 - INFO -   日期后缀: 20250729
2025-07-29 17:45:29 - INFO -   关键词: ['MonkeyAEE', 'Result', 'AIMonkey']
2025-07-29 17:45:29 - INFO - [搜索Excel文件: AIMonkey] 步骤 2: 设置搜索条件: 日期=20250729, 关键词=['MonkeyAEE', 'Result', 'AIMonkey']
2025-07-29 17:45:29 - INFO - ✓ 找到匹配文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx
2025-07-29 17:45:29 - INFO - ✓ 找到匹配文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx
2025-07-29 17:45:29 - INFO - 在 D:\Monkey\tOS16.0\AIMonkey 中找到 2 个匹配的Excel文件
2025-07-29 17:45:29 - INFO - 找到的文件列表:
2025-07-29 17:45:29 - INFO -   1. Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx (182.5 KB)
2025-07-29 17:45:29 - INFO -   2. Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx (457.0 KB)
2025-07-29 17:45:29 - INFO - 操作完成: 搜索Excel文件: AIMonkey - 找到 2 个匹配文件 (耗时: 0.005s, 步骤数: 2)
2025-07-29 17:45:29 - INFO - 开始操作: 关键词搜索: Result, MonkeyAEE, SH
2025-07-29 17:45:29 - INFO - 关键词搜索配置:
2025-07-29 17:45:29 - INFO -   目录: D:\Monkey\tOS16.0\AIMonkey
2025-07-29 17:45:29 - INFO -   关键词: ['Result', 'MonkeyAEE', 'SH']
2025-07-29 17:45:29 - INFO -   扩展名: ['.xls', '.xlsx']
2025-07-29 17:45:29 - INFO -   日期模式: ['20250729', '2025_07_29', '2025.07.29']
2025-07-29 17:45:29 - INFO - [关键词搜索: Result, MonkeyAEE, SH] 步骤 1: 搜索关键词: ['Result', 'MonkeyAEE', 'SH'], 扩展名: ['.xls', '.xlsx']
2025-07-29 17:45:29 - INFO - 在 D:\Monkey\tOS16.0\AIMonkey 中找到 9 个匹配的文件
2025-07-29 17:45:29 - INFO - 找到的文件（按匹配度排序）:
2025-07-29 17:45:29 - INFO -   1. Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx (分数: 4)
2025-07-29 17:45:29 - INFO -      大小: 182.5 KB
2025-07-29 17:45:29 - INFO -      匹配: 关键词: Result, 关键词: SH, 日期: 20250729
2025-07-29 17:45:29 - INFO -   2. Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx (分数: 4)
2025-07-29 17:45:29 - INFO -      大小: 457.0 KB
2025-07-29 17:45:29 - INFO -      匹配: 关键词: Result, 关键词: SH, 日期: 20250729
2025-07-29 17:45:29 - INFO -   3. Result_None_None_MonkeyAEE_SH_20250617.xlsx (分数: 3)
2025-07-29 17:45:29 - INFO -      大小: 29.5 KB
2025-07-29 17:45:29 - INFO -      匹配: 关键词: Result, 关键词: MonkeyAEE, 关键词: SH
2025-07-29 17:45:29 - INFO -   4. Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx (分数: 3)
2025-07-29 17:45:29 - INFO -      大小: 17.5 KB
2025-07-29 17:45:29 - INFO -      匹配: 关键词: Result, 关键词: MonkeyAEE, 关键词: SH
2025-07-29 17:45:29 - INFO -   5. Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls (分数: 3)
2025-07-29 17:45:29 - INFO -      大小: 30.5 KB
2025-07-29 17:45:29 - INFO -      匹配: 关键词: Result, 关键词: MonkeyAEE, 关键词: SH
2025-07-29 17:45:29 - INFO -   6. Result_None_None_MonkeyAEE_SH_20250617_corrected.xls (分数: 3)
2025-07-29 17:45:29 - INFO -      大小: 17.5 KB
2025-07-29 17:45:29 - INFO -      匹配: 关键词: Result, 关键词: MonkeyAEE, 关键词: SH
2025-07-29 17:45:29 - INFO -   7. Result_None_None_MonkeyAEE_SH_20250617_org.xlsx (分数: 3)
2025-07-29 17:45:29 - INFO -      大小: 21.5 KB
2025-07-29 17:45:29 - INFO -      匹配: 关键词: Result, 关键词: MonkeyAEE, 关键词: SH
2025-07-29 17:45:29 - INFO -   8. Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls (分数: 3)
2025-07-29 17:45:29 - INFO -      大小: 21.5 KB
2025-07-29 17:45:29 - INFO -      匹配: 关键词: Result, 关键词: MonkeyAEE, 关键词: SH
2025-07-29 17:45:29 - INFO -   9. Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls (分数: 2)
2025-07-29 17:45:29 - INFO -      大小: 158.5 KB
2025-07-29 17:45:29 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:45:29 - INFO - 操作完成: 关键词搜索: Result, MonkeyAEE, SH - 找到 9 个匹配文件 (耗时: 0.024s, 步骤数: 1)
2025-07-29 17:45:29 - INFO - 找到 15 个目标Excel文件，开始分析...
2025-07-29 17:45:29 - INFO - 开始操作: 批量Excel分析
2025-07-29 17:45:29 - INFO - 开始批量分析 15 个Excel文件
2025-07-29 17:45:29 - INFO - ============================================================
2025-07-29 17:45:29 - INFO - [批量Excel分析] 步骤 1: 开始批量分析 15 个文件
2025-07-29 17:45:29 - INFO - [批量Excel分析] 步骤 2: 预加载Package-Owner映射
2025-07-29 17:45:29 - INFO - 
[1/15] 处理文件: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls
2025-07-29 17:45:29 - INFO - 开始操作: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls
2025-07-29 17:45:29 - INFO - 开始分析Excel文件: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls
2025-07-29 17:45:29 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls] 步骤 1: 开始分析文件
2025-07-29 17:45:29 - INFO -   检测文件格式...
2025-07-29 17:45:29 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls] 步骤 2: 检测文件格式
2025-07-29 17:45:29 - INFO -   文件扩展名: .xls
2025-07-29 17:45:29 - INFO -   实际格式: xls
2025-07-29 17:45:29 - INFO -   使用引擎: xlrd
2025-07-29 17:45:29 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls] 步骤 3: 使用 xlrd 引擎读取
2025-07-29 17:45:29 - INFO -   文件读取成功，共 3 行 12 列
2025-07-29 17:45:29 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls] 步骤 4: 文件读取成功: 3 行 12 列
2025-07-29 17:45:29 - INFO -   找到Package列: 'Package'
2025-07-29 17:45:29 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:45:29 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:45:29 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:45:29 - INFO -   列名识别结果:
2025-07-29 17:45:29 - INFO -     Package列: Package
2025-07-29 17:45:29 - INFO -     CausedBy列: CausedBy
2025-07-29 17:45:29 - INFO -     Version列: Version
2025-07-29 17:45:29 - INFO -     Path列: Path
2025-07-29 17:45:29 - INFO -   开始数据预处理...
2025-07-29 17:45:29 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls] 步骤 5: 开始数据预处理
2025-07-29 17:45:29 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls] 步骤 6: 加载Package-Owner映射
2025-07-29 17:45:29 - INFO -   开始批量处理 3 行数据...
2025-07-29 17:45:29 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls] 步骤 7: 开始批量处理 3 行数据
2025-07-29 17:45:29 - INFO -   分析完成:
2025-07-29 17:45:29 - INFO -     总记录数: 3
2025-07-29 17:45:29 - INFO -     唯一Package数: 3
2025-07-29 17:45:29 - INFO -     涉及团队数: 2
2025-07-29 17:45:29 - INFO -     未知团队记录: 2
2025-07-29 17:45:29 - INFO -     Top 5 Package:
2025-07-29 17:45:29 - INFO -       com.android.settings: 1 条记录
2025-07-29 17:45:29 - INFO -       com.transsion.cloudserver: 1 条记录
2025-07-29 17:45:29 - INFO -       com.trassion.infinix.xclub: 1 条记录
2025-07-29 17:45:29 - INFO - Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls - 成功 - 3 条记录
2025-07-29 17:45:29 - INFO - 操作完成: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls - 分析完成: 3 条记录 (耗时: 0.043s, 步骤数: 7)
2025-07-29 17:45:29 - INFO -   ✓ 成功分析，获得 3 条记录
2025-07-29 17:45:29 - INFO - 
[2/15] 处理文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls
2025-07-29 17:45:29 - INFO - 开始操作: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls
2025-07-29 17:45:29 - INFO - 开始分析Excel文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls
2025-07-29 17:45:29 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls] 步骤 1: 开始分析文件
2025-07-29 17:45:29 - INFO -   检测文件格式...
2025-07-29 17:45:29 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls] 步骤 2: 检测文件格式
2025-07-29 17:45:29 - INFO -   文件扩展名: .xls
2025-07-29 17:45:29 - INFO -   实际格式: xls
2025-07-29 17:45:29 - INFO -   使用引擎: xlrd
2025-07-29 17:45:29 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls] 步骤 3: 使用 xlrd 引擎读取
2025-07-29 17:45:29 - INFO -   文件读取成功，共 29 行 14 列
2025-07-29 17:45:29 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls] 步骤 4: 文件读取成功: 29 行 14 列
2025-07-29 17:45:29 - INFO -   找到Package列: 'Package'
2025-07-29 17:45:29 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:45:29 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:45:29 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:45:29 - INFO -   列名识别结果:
2025-07-29 17:45:29 - INFO -     Package列: Package
2025-07-29 17:45:29 - INFO -     CausedBy列: CausedBy
2025-07-29 17:45:29 - INFO -     Version列: Version
2025-07-29 17:45:29 - INFO -     Path列: Path
2025-07-29 17:45:29 - INFO -   开始数据预处理...
2025-07-29 17:45:29 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls] 步骤 5: 开始数据预处理
2025-07-29 17:45:29 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls] 步骤 6: 加载Package-Owner映射
2025-07-29 17:45:29 - INFO -   开始批量处理 29 行数据...
2025-07-29 17:45:29 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls] 步骤 7: 开始批量处理 29 行数据
2025-07-29 17:45:29 - INFO -   分析完成:
2025-07-29 17:45:29 - INFO -     总记录数: 29
2025-07-29 17:45:29 - INFO -     唯一Package数: 14
2025-07-29 17:45:29 - INFO -     涉及团队数: 4
2025-07-29 17:45:29 - INFO -     未知团队记录: 23
2025-07-29 17:45:29 - INFO -     Top 5 Package:
2025-07-29 17:45:29 - INFO -       com.transsion.airtransfer: 7 条记录
2025-07-29 17:45:29 - INFO -       com.android.settings: 4 条记录
2025-07-29 17:45:29 - INFO -       com.gallery20: 4 条记录
2025-07-29 17:45:29 - INFO -       com.android.settings.intelligence: 3 条记录
2025-07-29 17:45:29 - INFO -       com.transsion.aivoiceassistant: 2 条记录
2025-07-29 17:45:29 - INFO - Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls - 成功 - 29 条记录
2025-07-29 17:45:29 - INFO - 操作完成: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls - 分析完成: 29 条记录 (耗时: 0.024s, 步骤数: 7)
2025-07-29 17:45:29 - INFO -   ✓ 成功分析，获得 29 条记录
2025-07-29 17:45:29 - INFO - 
[3/15] 处理文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls
2025-07-29 17:45:29 - INFO - 开始操作: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls
2025-07-29 17:45:29 - INFO - 开始分析Excel文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls
2025-07-29 17:45:29 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls] 步骤 1: 开始分析文件
2025-07-29 17:45:29 - INFO -   检测文件格式...
2025-07-29 17:45:29 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls] 步骤 2: 检测文件格式
2025-07-29 17:45:29 - INFO -   文件扩展名: .xls
2025-07-29 17:45:29 - INFO -   实际格式: xls
2025-07-29 17:45:29 - INFO -   使用引擎: xlrd
2025-07-29 17:45:29 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls] 步骤 3: 使用 xlrd 引擎读取
2025-07-29 17:45:29 - INFO -   文件读取成功，共 43 行 14 列
2025-07-29 17:45:29 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls] 步骤 4: 文件读取成功: 43 行 14 列
2025-07-29 17:45:29 - INFO -   找到Package列: 'Package'
2025-07-29 17:45:29 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:45:29 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:45:29 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:45:29 - INFO -   列名识别结果:
2025-07-29 17:45:29 - INFO -     Package列: Package
2025-07-29 17:45:29 - INFO -     CausedBy列: CausedBy
2025-07-29 17:45:29 - INFO -     Version列: Version
2025-07-29 17:45:29 - INFO -     Path列: Path
2025-07-29 17:45:29 - INFO -   开始数据预处理...
2025-07-29 17:45:29 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls] 步骤 5: 开始数据预处理
2025-07-29 17:45:29 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls] 步骤 6: 加载Package-Owner映射
2025-07-29 17:45:29 - INFO -   开始批量处理 43 行数据...
2025-07-29 17:45:29 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls] 步骤 7: 开始批量处理 43 行数据
2025-07-29 17:45:29 - INFO -   分析完成:
2025-07-29 17:45:29 - INFO -     总记录数: 43
2025-07-29 17:45:29 - INFO -     唯一Package数: 13
2025-07-29 17:45:29 - INFO -     涉及团队数: 4
2025-07-29 17:45:29 - INFO -     未知团队记录: 19
2025-07-29 17:45:29 - INFO -     Top 5 Package:
2025-07-29 17:45:29 - INFO -       com.android.settings: 18 条记录
2025-07-29 17:45:29 - INFO -       com.transsion.launcher3: 4 条记录
2025-07-29 17:45:29 - INFO -       com.android.chrome: 4 条记录
2025-07-29 17:45:29 - INFO -       com.sh.smart.caller: 3 条记录
2025-07-29 17:45:29 - INFO -       com.transsion.childmode: 3 条记录
2025-07-29 17:45:29 - INFO - Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls - 成功 - 43 条记录
2025-07-29 17:45:29 - INFO - 操作完成: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls - 分析完成: 43 条记录 (耗时: 0.024s, 步骤数: 7)
2025-07-29 17:45:29 - INFO -   ✓ 成功分析，获得 43 条记录
2025-07-29 17:45:29 - INFO - 
[4/15] 处理文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx
2025-07-29 17:45:29 - INFO - 开始操作: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx
2025-07-29 17:45:29 - INFO - 开始分析Excel文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx
2025-07-29 17:45:29 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 1: 开始分析文件
2025-07-29 17:45:29 - INFO -   检测文件格式...
2025-07-29 17:45:29 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 2: 检测文件格式
2025-07-29 17:45:29 - INFO -   文件扩展名: .xlsx
2025-07-29 17:45:29 - INFO -   实际格式: xls
2025-07-29 17:45:29 - INFO -   ✓ 创建格式正确的文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_corrected.xls
2025-07-29 17:45:29 - INFO -   使用修复后的文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_corrected.xls
2025-07-29 17:45:29 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 3: 使用修复后的文件
2025-07-29 17:45:29 - INFO -   使用引擎: xlrd
2025-07-29 17:45:29 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 4: 使用 xlrd 引擎读取
2025-07-29 17:45:29 - INFO -   文件读取成功，共 23 行 14 列
2025-07-29 17:45:29 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 5: 文件读取成功: 23 行 14 列
2025-07-29 17:45:29 - INFO -   找到Package列: 'Package'
2025-07-29 17:45:29 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:45:29 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:45:29 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:45:29 - INFO -   列名识别结果:
2025-07-29 17:45:29 - INFO -     Package列: Package
2025-07-29 17:45:29 - INFO -     CausedBy列: CausedBy
2025-07-29 17:45:29 - INFO -     Version列: Version
2025-07-29 17:45:29 - INFO -     Path列: Path
2025-07-29 17:45:29 - INFO -   开始数据预处理...
2025-07-29 17:45:29 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 6: 开始数据预处理
2025-07-29 17:45:29 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 7: 加载Package-Owner映射
2025-07-29 17:45:29 - INFO -   开始批量处理 23 行数据...
2025-07-29 17:45:29 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 8: 开始批量处理 23 行数据
2025-07-29 17:45:29 - INFO -   分析完成:
2025-07-29 17:45:29 - INFO -     总记录数: 23
2025-07-29 17:45:29 - INFO -     唯一Package数: 8
2025-07-29 17:45:29 - INFO -     涉及团队数: 4
2025-07-29 17:45:29 - INFO -     未知团队记录: 7
2025-07-29 17:45:29 - INFO -     Top 5 Package:
2025-07-29 17:45:29 - INFO -       com.android.settings: 10 条记录
2025-07-29 17:45:29 - INFO -       com.android.phone: 4 条记录
2025-07-29 17:45:29 - INFO -       com.android.settings.intelligence: 2 条记录
2025-07-29 17:45:29 - INFO -       com.android.systemui: 2 条记录
2025-07-29 17:45:29 - INFO -       com.transsion.launcher3: 2 条记录
2025-07-29 17:45:29 - INFO - Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_corrected.xls - 成功 - 23 条记录
2025-07-29 17:45:29 - INFO - 操作完成: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx - 分析完成: 23 条记录 (耗时: 0.021s, 步骤数: 8)
2025-07-29 17:45:29 - INFO -   ✓ 成功分析，获得 23 条记录
2025-07-29 17:45:29 - INFO - 
[5/15] 处理文件: statistics.txt
2025-07-29 17:45:29 - INFO - 开始操作: Excel分析: statistics.txt
2025-07-29 17:45:29 - INFO - 开始分析Excel文件: statistics.txt
2025-07-29 17:45:29 - INFO - [Excel分析: statistics.txt] 步骤 1: 开始分析文件
2025-07-29 17:45:29 - INFO -   检测文件格式...
2025-07-29 17:45:29 - INFO - [Excel分析: statistics.txt] 步骤 2: 检测文件格式
2025-07-29 17:45:29 - INFO -   文件扩展名: .txt
2025-07-29 17:45:29 - INFO -   实际格式: unknown
2025-07-29 17:45:29 - INFO -   使用引擎: openpyxl
2025-07-29 17:45:29 - INFO - [Excel分析: statistics.txt] 步骤 3: 使用 openpyxl 引擎读取
2025-07-29 17:45:30 - WARNING -   openpyxl 引擎失败，尝试备用引擎: File is not a zip file
2025-07-29 17:45:30 - WARNING - [Excel分析: statistics.txt] 步骤 4: openpyxl 引擎失败，尝试备用引擎
2025-07-29 17:45:30 - INFO -   尝试备用引擎: xlrd
2025-07-29 17:45:30 - ERROR -   分析Excel文件失败 - D:\Monkey\tOS15.1\AIMonkey\2025-07-29\statistics.txt
2025-07-29 17:45:30 - ERROR -     异常详情: Unsupported format, or corrupt file: Expected BOF record; found b"['CM5', "
2025-07-29 17:45:30 - ERROR - Excel分析异常: D:\Monkey\tOS15.1\AIMonkey\2025-07-29\statistics.txt
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 893, in analyze_excel_file
    df = pd.read_excel(
         ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 495, in read_excel
    io = ExcelFile(
         ^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 553, in __init__
    super().__init__(
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 572, in load_workbook
    return load_workbook(
           ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 346, in load_workbook
    reader = ExcelReader(filename, read_only, keep_vba,
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 123, in __init__
    self.archive = _validate_archive(fn)
                   ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 95, in _validate_archive
    archive = ZipFile(filename, 'r')
              ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\SystemApplication\Lib\zipfile.py", line 1299, in __init__
    self._RealGetContents()
  File "D:\SystemApplication\Lib\zipfile.py", line 1366, in _RealGetContents
    raise BadZipFile("File is not a zip file")
zipfile.BadZipFile: File is not a zip file

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 908, in analyze_excel_file
    df = pd.read_excel(
         ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 495, in read_excel
    io = ExcelFile(
         ^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_xlrd.py", line 46, in __init__
    super().__init__(
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_xlrd.py", line 63, in load_workbook
    return open_workbook(file_contents=data, **engine_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\__init__.py", line 172, in open_workbook
    bk = open_workbook_xls(
         ^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 79, in open_workbook_xls
    biff_version = bk.getbof(XL_WORKBOOK_GLOBALS)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 1284, in getbof
    bof_error('Expected BOF record; found %r' % self.mem[savpos:savpos+8])
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 1278, in bof_error
    raise XLRDError('Unsupported format, or corrupt file: ' + msg)
xlrd.biffh.XLRDError: Unsupported format, or corrupt file: Expected BOF record; found b"['CM5', "
2025-07-29 17:45:30 - ERROR - Excel分析: statistics.txt - 失败 - 分析异常: Unsupported format, or corrupt file: Expected BOF record; found b"['CM5', "
2025-07-29 17:45:30 - ERROR - 操作失败: Excel分析: statistics.txt - 分析异常: Unsupported format, or corrupt file: Expected BOF record; found b"['CM5', " (耗时: 0.212s, 步骤数: 4)
2025-07-29 17:45:30 - WARNING -   ✗ 分析失败或无有效数据
2025-07-29 17:45:30 - INFO - 
[6/15] 处理文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx
2025-07-29 17:45:30 - INFO - 开始操作: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx
2025-07-29 17:45:30 - INFO - 开始分析Excel文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 1: 开始分析文件
2025-07-29 17:45:30 - INFO -   检测文件格式...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 2: 检测文件格式
2025-07-29 17:45:30 - INFO -   文件扩展名: .xlsx
2025-07-29 17:45:30 - INFO -   实际格式: xls
2025-07-29 17:45:30 - INFO -   ✓ 创建格式正确的文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_corrected.xls
2025-07-29 17:45:30 - INFO -   使用修复后的文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_corrected.xls
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 3: 使用修复后的文件
2025-07-29 17:45:30 - INFO -   使用引擎: xlrd
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 4: 使用 xlrd 引擎读取
2025-07-29 17:45:30 - INFO -   文件读取成功，共 4 行 14 列
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 5: 文件读取成功: 4 行 14 列
2025-07-29 17:45:30 - INFO -   找到Package列: 'Package'
2025-07-29 17:45:30 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:45:30 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:45:30 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:45:30 - INFO -   列名识别结果:
2025-07-29 17:45:30 - INFO -     Package列: Package
2025-07-29 17:45:30 - INFO -     CausedBy列: CausedBy
2025-07-29 17:45:30 - INFO -     Version列: Version
2025-07-29 17:45:30 - INFO -     Path列: Path
2025-07-29 17:45:30 - INFO -   开始数据预处理...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 6: 开始数据预处理
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 7: 加载Package-Owner映射
2025-07-29 17:45:30 - INFO -   开始批量处理 4 行数据...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 8: 开始批量处理 4 行数据
2025-07-29 17:45:30 - INFO -   分析完成:
2025-07-29 17:45:30 - INFO -     总记录数: 4
2025-07-29 17:45:30 - INFO -     唯一Package数: 2
2025-07-29 17:45:30 - INFO -     涉及团队数: 1
2025-07-29 17:45:30 - INFO -     未知团队记录: 4
2025-07-29 17:45:30 - INFO -     Top 5 Package:
2025-07-29 17:45:30 - INFO -       com.transsion.airtransfer: 3 条记录
2025-07-29 17:45:30 - INFO -       com.transsion.cloudserver: 1 条记录
2025-07-29 17:45:30 - INFO - Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_corrected.xls - 成功 - 4 条记录
2025-07-29 17:45:30 - INFO - 操作完成: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx - 分析完成: 4 条记录 (耗时: 0.020s, 步骤数: 8)
2025-07-29 17:45:30 - INFO -   ✓ 成功分析，获得 4 条记录
2025-07-29 17:45:30 - INFO - 
[7/15] 处理文件: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx
2025-07-29 17:45:30 - INFO - 开始操作: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx
2025-07-29 17:45:30 - INFO - 开始分析Excel文件: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx] 步骤 1: 开始分析文件
2025-07-29 17:45:30 - INFO -   检测文件格式...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx] 步骤 2: 检测文件格式
2025-07-29 17:45:30 - INFO -   文件扩展名: .xlsx
2025-07-29 17:45:30 - INFO -   实际格式: xls
2025-07-29 17:45:30 - INFO -   ✓ 创建格式正确的文件: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls
2025-07-29 17:45:30 - INFO -   使用修复后的文件: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx] 步骤 3: 使用修复后的文件
2025-07-29 17:45:30 - INFO -   使用引擎: xlrd
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx] 步骤 4: 使用 xlrd 引擎读取
2025-07-29 17:45:30 - INFO -   文件读取成功，共 2 行 12 列
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx] 步骤 5: 文件读取成功: 2 行 12 列
2025-07-29 17:45:30 - INFO -   找到Package列: 'Package'
2025-07-29 17:45:30 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:45:30 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:45:30 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:45:30 - INFO -   列名识别结果:
2025-07-29 17:45:30 - INFO -     Package列: Package
2025-07-29 17:45:30 - INFO -     CausedBy列: CausedBy
2025-07-29 17:45:30 - INFO -     Version列: Version
2025-07-29 17:45:30 - INFO -     Path列: Path
2025-07-29 17:45:30 - INFO -   开始数据预处理...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx] 步骤 6: 开始数据预处理
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx] 步骤 7: 加载Package-Owner映射
2025-07-29 17:45:30 - INFO -   开始批量处理 2 行数据...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx] 步骤 8: 开始批量处理 2 行数据
2025-07-29 17:45:30 - INFO -   分析完成:
2025-07-29 17:45:30 - INFO -     总记录数: 2
2025-07-29 17:45:30 - INFO -     唯一Package数: 2
2025-07-29 17:45:30 - INFO -     涉及团队数: 2
2025-07-29 17:45:30 - INFO -     未知团队记录: 1
2025-07-29 17:45:30 - INFO -     Top 5 Package:
2025-07-29 17:45:30 - INFO -       com.android.settings: 1 条记录
2025-07-29 17:45:30 - INFO -       com.trassion.infinix.xclub: 1 条记录
2025-07-29 17:45:30 - INFO - Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls - 成功 - 2 条记录
2025-07-29 17:45:30 - INFO - 操作完成: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx - 分析完成: 2 条记录 (耗时: 0.025s, 步骤数: 8)
2025-07-29 17:45:30 - INFO -   ✓ 成功分析，获得 2 条记录
2025-07-29 17:45:30 - INFO - 
[8/15] 处理文件: Result_None_None_MonkeyAEE_SH_20250617.xlsx
2025-07-29 17:45:30 - INFO - 开始操作: Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xlsx
2025-07-29 17:45:30 - INFO - 开始分析Excel文件: Result_None_None_MonkeyAEE_SH_20250617.xlsx
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xlsx] 步骤 1: 开始分析文件
2025-07-29 17:45:30 - INFO -   检测文件格式...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xlsx] 步骤 2: 检测文件格式
2025-07-29 17:45:30 - INFO -   文件扩展名: .xlsx
2025-07-29 17:45:30 - INFO -   实际格式: xls
2025-07-29 17:45:30 - INFO -   ✓ 创建格式正确的文件: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls
2025-07-29 17:45:30 - INFO -   使用修复后的文件: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xlsx] 步骤 3: 使用修复后的文件
2025-07-29 17:45:30 - INFO -   使用引擎: xlrd
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xlsx] 步骤 4: 使用 xlrd 引擎读取
2025-07-29 17:45:30 - INFO -   文件读取成功，共 2 行 12 列
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xlsx] 步骤 5: 文件读取成功: 2 行 12 列
2025-07-29 17:45:30 - INFO -   找到Package列: 'Package'
2025-07-29 17:45:30 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:45:30 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:45:30 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:45:30 - INFO -   列名识别结果:
2025-07-29 17:45:30 - INFO -     Package列: Package
2025-07-29 17:45:30 - INFO -     CausedBy列: CausedBy
2025-07-29 17:45:30 - INFO -     Version列: Version
2025-07-29 17:45:30 - INFO -     Path列: Path
2025-07-29 17:45:30 - INFO -   开始数据预处理...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xlsx] 步骤 6: 开始数据预处理
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xlsx] 步骤 7: 加载Package-Owner映射
2025-07-29 17:45:30 - INFO -   开始批量处理 2 行数据...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xlsx] 步骤 8: 开始批量处理 2 行数据
2025-07-29 17:45:30 - INFO -   分析完成:
2025-07-29 17:45:30 - INFO -     总记录数: 2
2025-07-29 17:45:30 - INFO -     唯一Package数: 2
2025-07-29 17:45:30 - INFO -     涉及团队数: 2
2025-07-29 17:45:30 - INFO -     未知团队记录: 1
2025-07-29 17:45:30 - INFO -     Top 5 Package:
2025-07-29 17:45:30 - INFO -       com.android.settings: 1 条记录
2025-07-29 17:45:30 - INFO -       com.trassion.infinix.xclub: 1 条记录
2025-07-29 17:45:30 - INFO - Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls - 成功 - 2 条记录
2025-07-29 17:45:30 - INFO - 操作完成: Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xlsx - 分析完成: 2 条记录 (耗时: 0.027s, 步骤数: 8)
2025-07-29 17:45:30 - INFO -   ✓ 成功分析，获得 2 条记录
2025-07-29 17:45:30 - INFO - 
[9/15] 处理文件: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx
2025-07-29 17:45:30 - INFO - 开始操作: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx
2025-07-29 17:45:30 - INFO - 开始分析Excel文件: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx] 步骤 1: 开始分析文件
2025-07-29 17:45:30 - INFO -   检测文件格式...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx] 步骤 2: 检测文件格式
2025-07-29 17:45:30 - INFO -   文件扩展名: .xlsx
2025-07-29 17:45:30 - INFO -   实际格式: xls
2025-07-29 17:45:30 - INFO -   ✓ 创建格式正确的文件: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls
2025-07-29 17:45:30 - INFO -   使用修复后的文件: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx] 步骤 3: 使用修复后的文件
2025-07-29 17:45:30 - INFO -   使用引擎: xlrd
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx] 步骤 4: 使用 xlrd 引擎读取
2025-07-29 17:45:30 - INFO -   文件读取成功，共 3 行 12 列
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx] 步骤 5: 文件读取成功: 3 行 12 列
2025-07-29 17:45:30 - INFO -   找到Package列: 'Package'
2025-07-29 17:45:30 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:45:30 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:45:30 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:45:30 - INFO -   列名识别结果:
2025-07-29 17:45:30 - INFO -     Package列: Package
2025-07-29 17:45:30 - INFO -     CausedBy列: CausedBy
2025-07-29 17:45:30 - INFO -     Version列: Version
2025-07-29 17:45:30 - INFO -     Path列: Path
2025-07-29 17:45:30 - INFO -   开始数据预处理...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx] 步骤 6: 开始数据预处理
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx] 步骤 7: 加载Package-Owner映射
2025-07-29 17:45:30 - INFO -   开始批量处理 3 行数据...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx] 步骤 8: 开始批量处理 3 行数据
2025-07-29 17:45:30 - INFO -   分析完成:
2025-07-29 17:45:30 - INFO -     总记录数: 3
2025-07-29 17:45:30 - INFO -     唯一Package数: 3
2025-07-29 17:45:30 - INFO -     涉及团队数: 2
2025-07-29 17:45:30 - INFO -     未知团队记录: 2
2025-07-29 17:45:30 - INFO -     Top 5 Package:
2025-07-29 17:45:30 - INFO -       com.android.settings: 1 条记录
2025-07-29 17:45:30 - INFO -       com.transsion.cloudserver: 1 条记录
2025-07-29 17:45:30 - INFO -       com.trassion.infinix.xclub: 1 条记录
2025-07-29 17:45:30 - INFO - Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls - 成功 - 3 条记录
2025-07-29 17:45:30 - INFO - 操作完成: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx - 分析完成: 3 条记录 (耗时: 0.022s, 步骤数: 8)
2025-07-29 17:45:30 - INFO -   ✓ 成功分析，获得 3 条记录
2025-07-29 17:45:30 - INFO - 
[10/15] 处理文件: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls
2025-07-29 17:45:30 - INFO - 开始操作: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls
2025-07-29 17:45:30 - INFO - 开始分析Excel文件: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls] 步骤 1: 开始分析文件
2025-07-29 17:45:30 - INFO -   检测文件格式...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls] 步骤 2: 检测文件格式
2025-07-29 17:45:30 - INFO -   文件扩展名: .xls
2025-07-29 17:45:30 - INFO -   实际格式: xls
2025-07-29 17:45:30 - INFO -   使用引擎: xlrd
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls] 步骤 3: 使用 xlrd 引擎读取
2025-07-29 17:45:30 - INFO -   文件读取成功，共 2 行 12 列
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls] 步骤 4: 文件读取成功: 2 行 12 列
2025-07-29 17:45:30 - INFO -   找到Package列: 'Package'
2025-07-29 17:45:30 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:45:30 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:45:30 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:45:30 - INFO -   列名识别结果:
2025-07-29 17:45:30 - INFO -     Package列: Package
2025-07-29 17:45:30 - INFO -     CausedBy列: CausedBy
2025-07-29 17:45:30 - INFO -     Version列: Version
2025-07-29 17:45:30 - INFO -     Path列: Path
2025-07-29 17:45:30 - INFO -   开始数据预处理...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls] 步骤 5: 开始数据预处理
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls] 步骤 6: 加载Package-Owner映射
2025-07-29 17:45:30 - INFO -   开始批量处理 2 行数据...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls] 步骤 7: 开始批量处理 2 行数据
2025-07-29 17:45:30 - INFO -   分析完成:
2025-07-29 17:45:30 - INFO -     总记录数: 2
2025-07-29 17:45:30 - INFO -     唯一Package数: 2
2025-07-29 17:45:30 - INFO -     涉及团队数: 2
2025-07-29 17:45:30 - INFO -     未知团队记录: 1
2025-07-29 17:45:30 - INFO -     Top 5 Package:
2025-07-29 17:45:30 - INFO -       com.android.settings: 1 条记录
2025-07-29 17:45:30 - INFO -       com.trassion.infinix.xclub: 1 条记录
2025-07-29 17:45:30 - INFO - Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls - 成功 - 2 条记录
2025-07-29 17:45:30 - INFO - 操作完成: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls - 分析完成: 2 条记录 (耗时: 0.024s, 步骤数: 7)
2025-07-29 17:45:30 - INFO -   ✓ 成功分析，获得 2 条记录
2025-07-29 17:45:30 - INFO - 
[11/15] 处理文件: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls
2025-07-29 17:45:30 - INFO - 开始操作: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls
2025-07-29 17:45:30 - INFO - 开始分析Excel文件: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls] 步骤 1: 开始分析文件
2025-07-29 17:45:30 - INFO -   检测文件格式...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls] 步骤 2: 检测文件格式
2025-07-29 17:45:30 - INFO -   文件扩展名: .xls
2025-07-29 17:45:30 - INFO -   实际格式: xls
2025-07-29 17:45:30 - INFO -   使用引擎: xlrd
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls] 步骤 3: 使用 xlrd 引擎读取
2025-07-29 17:45:30 - INFO -   文件读取成功，共 2 行 12 列
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls] 步骤 4: 文件读取成功: 2 行 12 列
2025-07-29 17:45:30 - INFO -   找到Package列: 'Package'
2025-07-29 17:45:30 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:45:30 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:45:30 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:45:30 - INFO -   列名识别结果:
2025-07-29 17:45:30 - INFO -     Package列: Package
2025-07-29 17:45:30 - INFO -     CausedBy列: CausedBy
2025-07-29 17:45:30 - INFO -     Version列: Version
2025-07-29 17:45:30 - INFO -     Path列: Path
2025-07-29 17:45:30 - INFO -   开始数据预处理...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls] 步骤 5: 开始数据预处理
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls] 步骤 6: 加载Package-Owner映射
2025-07-29 17:45:30 - INFO -   开始批量处理 2 行数据...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls] 步骤 7: 开始批量处理 2 行数据
2025-07-29 17:45:30 - INFO -   分析完成:
2025-07-29 17:45:30 - INFO -     总记录数: 2
2025-07-29 17:45:30 - INFO -     唯一Package数: 2
2025-07-29 17:45:30 - INFO -     涉及团队数: 2
2025-07-29 17:45:30 - INFO -     未知团队记录: 1
2025-07-29 17:45:30 - INFO -     Top 5 Package:
2025-07-29 17:45:30 - INFO -       com.android.settings: 1 条记录
2025-07-29 17:45:30 - INFO -       com.trassion.infinix.xclub: 1 条记录
2025-07-29 17:45:30 - INFO - Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls - 成功 - 2 条记录
2025-07-29 17:45:30 - INFO - 操作完成: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls - 分析完成: 2 条记录 (耗时: 0.021s, 步骤数: 7)
2025-07-29 17:45:30 - INFO -   ✓ 成功分析，获得 2 条记录
2025-07-29 17:45:30 - INFO - 
[12/15] 处理文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls
2025-07-29 17:45:30 - INFO - 开始操作: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls
2025-07-29 17:45:30 - INFO - 开始分析Excel文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls] 步骤 1: 开始分析文件
2025-07-29 17:45:30 - INFO -   检测文件格式...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls] 步骤 2: 检测文件格式
2025-07-29 17:45:30 - INFO -   文件扩展名: .xls
2025-07-29 17:45:30 - INFO -   实际格式: xls
2025-07-29 17:45:30 - INFO -   使用引擎: xlrd
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls] 步骤 3: 使用 xlrd 引擎读取
2025-07-29 17:45:30 - INFO -   文件读取成功，共 7 行 14 列
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls] 步骤 4: 文件读取成功: 7 行 14 列
2025-07-29 17:45:30 - INFO -   找到Package列: 'Package'
2025-07-29 17:45:30 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:45:30 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:45:30 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:45:30 - INFO -   列名识别结果:
2025-07-29 17:45:30 - INFO -     Package列: Package
2025-07-29 17:45:30 - INFO -     CausedBy列: CausedBy
2025-07-29 17:45:30 - INFO -     Version列: Version
2025-07-29 17:45:30 - INFO -     Path列: Path
2025-07-29 17:45:30 - INFO -   开始数据预处理...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls] 步骤 5: 开始数据预处理
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls] 步骤 6: 加载Package-Owner映射
2025-07-29 17:45:30 - INFO -   开始批量处理 7 行数据...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls] 步骤 7: 开始批量处理 7 行数据
2025-07-29 17:45:30 - INFO -   分析完成:
2025-07-29 17:45:30 - INFO -     总记录数: 7
2025-07-29 17:45:30 - INFO -     唯一Package数: 5
2025-07-29 17:45:30 - INFO -     涉及团队数: 1
2025-07-29 17:45:30 - INFO -     未知团队记录: 7
2025-07-29 17:45:30 - INFO -     Top 5 Package:
2025-07-29 17:45:30 - INFO -       com.android.settings.intelligence: 2 条记录
2025-07-29 17:45:30 - INFO -       com.transsion.airtransfer: 2 条记录
2025-07-29 17:45:30 - INFO -       com.transsion.cloudserver: 1 条记录
2025-07-29 17:45:30 - INFO -       com.transsion.launcher3: 1 条记录
2025-07-29 17:45:30 - INFO -       com.transsion.phonemaster: 1 条记录
2025-07-29 17:45:30 - INFO - Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls - 成功 - 7 条记录
2025-07-29 17:45:30 - INFO - 操作完成: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls - 分析完成: 7 条记录 (耗时: 0.019s, 步骤数: 7)
2025-07-29 17:45:30 - INFO -   ✓ 成功分析，获得 7 条记录
2025-07-29 17:45:30 - INFO - 
[13/15] 处理文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx
2025-07-29 17:45:30 - INFO - 开始操作: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx
2025-07-29 17:45:30 - INFO - 开始分析Excel文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 1: 开始分析文件
2025-07-29 17:45:30 - INFO -   检测文件格式...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 2: 检测文件格式
2025-07-29 17:45:30 - INFO -   文件扩展名: .xlsx
2025-07-29 17:45:30 - INFO -   实际格式: xls
2025-07-29 17:45:30 - INFO -   ✓ 创建格式正确的文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls
2025-07-29 17:45:30 - INFO -   使用修复后的文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 3: 使用修复后的文件
2025-07-29 17:45:30 - INFO -   使用引擎: xlrd
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 4: 使用 xlrd 引擎读取
2025-07-29 17:45:30 - INFO -   文件读取成功，共 134 行 14 列
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 5: 文件读取成功: 134 行 14 列
2025-07-29 17:45:30 - INFO -   找到Package列: 'Package'
2025-07-29 17:45:30 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:45:30 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:45:30 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:45:30 - INFO -   列名识别结果:
2025-07-29 17:45:30 - INFO -     Package列: Package
2025-07-29 17:45:30 - INFO -     CausedBy列: CausedBy
2025-07-29 17:45:30 - INFO -     Version列: Version
2025-07-29 17:45:30 - INFO -     Path列: Path
2025-07-29 17:45:30 - INFO -   开始数据预处理...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 6: 开始数据预处理
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 7: 加载Package-Owner映射
2025-07-29 17:45:30 - INFO -   开始批量处理 134 行数据...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 8: 开始批量处理 134 行数据
2025-07-29 17:45:30 - INFO -   分析完成:
2025-07-29 17:45:30 - INFO -     总记录数: 134
2025-07-29 17:45:30 - INFO -     唯一Package数: 11
2025-07-29 17:45:30 - INFO -     涉及团队数: 3
2025-07-29 17:45:30 - INFO -     未知团队记录: 117
2025-07-29 17:45:30 - INFO -     Top 5 Package:
2025-07-29 17:45:30 - INFO -       com.google.android.apps.nbu.files: 37 条记录
2025-07-29 17:45:30 - INFO -       com.transsion.airtransfer: 30 条记录
2025-07-29 17:45:30 - INFO -       com.transsion.cloudserver: 28 条记录
2025-07-29 17:45:30 - INFO -       com.android.chrome: 16 条记录
2025-07-29 17:45:30 - INFO -       com.google.android.gm: 9 条记录
2025-07-29 17:45:30 - INFO - Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls - 成功 - 134 条记录
2025-07-29 17:45:30 - INFO - 操作完成: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx - 分析完成: 134 条记录 (耗时: 0.034s, 步骤数: 8)
2025-07-29 17:45:30 - INFO -   ✓ 成功分析，获得 134 条记录
2025-07-29 17:45:30 - INFO - 
[14/15] 处理文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx
2025-07-29 17:45:30 - INFO - 开始操作: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx
2025-07-29 17:45:30 - INFO - 开始分析Excel文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 1: 开始分析文件
2025-07-29 17:45:30 - INFO -   检测文件格式...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 2: 检测文件格式
2025-07-29 17:45:30 - INFO -   文件扩展名: .xlsx
2025-07-29 17:45:30 - INFO -   实际格式: xls
2025-07-29 17:45:30 - INFO -   ✓ 创建格式正确的文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls
2025-07-29 17:45:30 - INFO -   使用修复后的文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 3: 使用修复后的文件
2025-07-29 17:45:30 - INFO -   使用引擎: xlrd
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 4: 使用 xlrd 引擎读取
2025-07-29 17:45:30 - INFO -   文件读取成功，共 89 行 14 列
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 5: 文件读取成功: 89 行 14 列
2025-07-29 17:45:30 - INFO -   找到Package列: 'Package'
2025-07-29 17:45:30 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:45:30 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:45:30 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:45:30 - INFO -   列名识别结果:
2025-07-29 17:45:30 - INFO -     Package列: Package
2025-07-29 17:45:30 - INFO -     CausedBy列: CausedBy
2025-07-29 17:45:30 - INFO -     Version列: Version
2025-07-29 17:45:30 - INFO -     Path列: Path
2025-07-29 17:45:30 - INFO -   开始数据预处理...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 6: 开始数据预处理
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 7: 加载Package-Owner映射
2025-07-29 17:45:30 - INFO -   开始批量处理 89 行数据...
2025-07-29 17:45:30 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 8: 开始批量处理 89 行数据
2025-07-29 17:45:30 - INFO -   分析完成:
2025-07-29 17:45:30 - INFO -     总记录数: 89
2025-07-29 17:45:30 - INFO -     唯一Package数: 16
2025-07-29 17:45:30 - INFO -     涉及团队数: 7
2025-07-29 17:45:30 - INFO -     未知团队记录: 19
2025-07-29 17:45:30 - INFO -     Top 5 Package:
2025-07-29 17:45:30 - INFO -       com.android.settings: 46 条记录
2025-07-29 17:45:30 - INFO -       com.android.phone: 9 条记录
2025-07-29 17:45:30 - INFO -       com.android.chrome: 9 条记录
2025-07-29 17:45:30 - INFO -       com.android.settings.intelligence: 5 条记录
2025-07-29 17:45:30 - INFO -       com.transsion.launcher3: 4 条记录
2025-07-29 17:45:30 - INFO - Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls - 成功 - 89 条记录
2025-07-29 17:45:30 - INFO - 操作完成: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx - 分析完成: 89 条记录 (耗时: 0.028s, 步骤数: 8)
2025-07-29 17:45:30 - INFO -   ✓ 成功分析，获得 89 条记录
2025-07-29 17:45:30 - INFO - 
[15/15] 处理文件: statistics.txt
2025-07-29 17:45:30 - INFO - 开始操作: Excel分析: statistics.txt
2025-07-29 17:45:30 - INFO - 开始分析Excel文件: statistics.txt
2025-07-29 17:45:30 - INFO - [Excel分析: statistics.txt] 步骤 1: 开始分析文件
2025-07-29 17:45:30 - INFO -   检测文件格式...
2025-07-29 17:45:30 - INFO - [Excel分析: statistics.txt] 步骤 2: 检测文件格式
2025-07-29 17:45:30 - INFO -   文件扩展名: .txt
2025-07-29 17:45:30 - INFO -   实际格式: unknown
2025-07-29 17:45:30 - INFO -   使用引擎: openpyxl
2025-07-29 17:45:30 - INFO - [Excel分析: statistics.txt] 步骤 3: 使用 openpyxl 引擎读取
2025-07-29 17:45:30 - WARNING -   openpyxl 引擎失败，尝试备用引擎: File is not a zip file
2025-07-29 17:45:30 - WARNING - [Excel分析: statistics.txt] 步骤 4: openpyxl 引擎失败，尝试备用引擎
2025-07-29 17:45:30 - INFO -   尝试备用引擎: xlrd
2025-07-29 17:45:30 - ERROR -   分析Excel文件失败 - D:\Monkey\tOS16.0\AIMonkey\2025-07-29\statistics.txt
2025-07-29 17:45:30 - ERROR -     异常详情: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'"
2025-07-29 17:45:30 - ERROR - Excel分析异常: D:\Monkey\tOS16.0\AIMonkey\2025-07-29\statistics.txt
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 893, in analyze_excel_file
    df = pd.read_excel(
         ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 495, in read_excel
    io = ExcelFile(
         ^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 553, in __init__
    super().__init__(
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 572, in load_workbook
    return load_workbook(
           ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 346, in load_workbook
    reader = ExcelReader(filename, read_only, keep_vba,
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 123, in __init__
    self.archive = _validate_archive(fn)
                   ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 95, in _validate_archive
    archive = ZipFile(filename, 'r')
              ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\SystemApplication\Lib\zipfile.py", line 1299, in __init__
    self._RealGetContents()
  File "D:\SystemApplication\Lib\zipfile.py", line 1366, in _RealGetContents
    raise BadZipFile("File is not a zip file")
zipfile.BadZipFile: File is not a zip file

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 908, in analyze_excel_file
    df = pd.read_excel(
         ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 495, in read_excel
    io = ExcelFile(
         ^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_xlrd.py", line 46, in __init__
    super().__init__(
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_xlrd.py", line 63, in load_workbook
    return open_workbook(file_contents=data, **engine_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\__init__.py", line 172, in open_workbook
    bk = open_workbook_xls(
         ^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 79, in open_workbook_xls
    biff_version = bk.getbof(XL_WORKBOOK_GLOBALS)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 1284, in getbof
    bof_error('Expected BOF record; found %r' % self.mem[savpos:savpos+8])
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 1278, in bof_error
    raise XLRDError('Unsupported format, or corrupt file: ' + msg)
xlrd.biffh.XLRDError: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'"
2025-07-29 17:45:30 - ERROR - Excel分析: statistics.txt - 失败 - 分析异常: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'"
2025-07-29 17:45:30 - ERROR - 操作失败: Excel分析: statistics.txt - 分析异常: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'" (耗时: 0.006s, 步骤数: 4)
2025-07-29 17:45:30 - WARNING -   ✗ 分析失败或无有效数据
2025-07-29 17:45:30 - INFO - 
============================================================
2025-07-29 17:45:30 - INFO - 批量分析统计
2025-07-29 17:45:30 - INFO - ============================================================
2025-07-29 17:45:30 - INFO - 处理结果:
2025-07-29 17:45:30 - INFO -   成功文件: 13
2025-07-29 17:45:30 - INFO -   失败文件: 2
2025-07-29 17:45:30 - INFO -   总记录数: 343
2025-07-29 17:45:30 - INFO -   唯一Package数: 37
2025-07-29 17:45:30 - INFO -   涉及团队数: 8
2025-07-29 17:45:30 - INFO -   处理文件数: 10
2025-07-29 17:45:30 - INFO - 
Package分布 (Top 10):
2025-07-29 17:45:30 - INFO -   com.android.settings: 84 条记录
2025-07-29 17:45:30 - INFO -   com.transsion.airtransfer: 42 条记录
2025-07-29 17:45:30 - INFO -   com.google.android.apps.nbu.files: 37 条记录
2025-07-29 17:45:30 - INFO -   com.transsion.cloudserver: 33 条记录
2025-07-29 17:45:30 - INFO -   com.android.chrome: 29 条记录
2025-07-29 17:45:30 - INFO -   com.android.phone: 13 条记录
2025-07-29 17:45:30 - INFO -   com.android.settings.intelligence: 13 条记录
2025-07-29 17:45:30 - INFO -   com.transsion.launcher3: 11 条记录
2025-07-29 17:45:30 - INFO -   com.google.android.gm: 10 条记录
2025-07-29 17:45:30 - INFO -   com.transsion.autotest.fillphonetool2: 8 条记录
2025-07-29 17:45:30 - INFO - 
团队分布:
2025-07-29 17:45:30 - INFO -   未知团队: 204 条记录
2025-07-29 17:45:30 - INFO -   系统设置团队: 84 条记录
2025-07-29 17:45:30 - INFO -   浏览器团队: 29 条记录
2025-07-29 17:45:30 - INFO -   电话团队: 13 条记录
2025-07-29 17:45:30 - INFO -   系统UI团队: 7 条记录
2025-07-29 17:45:30 - INFO -   输入法团队: 3 条记录
2025-07-29 17:45:30 - INFO -   Google服务团队: 2 条记录
2025-07-29 17:45:30 - INFO -   相机团队: 1 条记录
2025-07-29 17:45:30 - INFO - 
文件分布:
2025-07-29 17:45:30 - INFO -   Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls: 134 条记录
2025-07-29 17:45:30 - INFO -   Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls: 89 条记录
2025-07-29 17:45:30 - INFO -   Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls: 43 条记录
2025-07-29 17:45:30 - INFO -   Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls: 29 条记录
2025-07-29 17:45:30 - INFO -   Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_corrected.xls: 23 条记录
2025-07-29 17:45:30 - INFO -   Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls: 7 条记录
2025-07-29 17:45:30 - INFO -   Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls: 6 条记录
2025-07-29 17:45:30 - INFO -   Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_corrected.xls: 4 条记录
2025-07-29 17:45:30 - INFO -   Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls: 4 条记录
2025-07-29 17:45:30 - INFO -   Result_None_None_MonkeyAEE_SH_20250617_corrected.xls: 4 条记录
2025-07-29 17:45:30 - INFO - 批量Excel分析: 总计 15, 成功 13, 失败 2
2025-07-29 17:45:30 - INFO - 操作完成: 批量Excel分析 - 批量分析完成: 成功 13/15 个文件，获得 343 条记录 (耗时: 0.590s, 步骤数: 2)
2025-07-29 17:45:30 - INFO - 分析完成，获得 343 条异常记录
2025-07-29 17:45:30 - INFO - [AIMonkey处理流程: 2025-07-29] 步骤 4: 分析完成: 343 条记录
2025-07-29 17:45:30 - INFO - 
步骤3: 生成摘要报告...
2025-07-29 17:45:30 - INFO - [AIMonkey处理流程: 2025-07-29] 步骤 5: 生成摘要报告
2025-07-29 17:45:30 - INFO - 开始操作: 生成摘要报告
2025-07-29 17:45:30 - INFO - 生成分析摘要报告...
2025-07-29 17:45:30 - INFO - [生成摘要报告] 步骤 1: 开始生成摘要报告
2025-07-29 17:45:30 - INFO - [生成摘要报告] 步骤 2: 分析 343 条记录
2025-07-29 17:45:30 - INFO - [生成摘要报告] 步骤 3: 计算基本统计信息
2025-07-29 17:45:30 - INFO - [生成摘要报告] 步骤 4: 分析Top Package
2025-07-29 17:45:30 - INFO - [生成摘要报告] 步骤 5: 分析团队分布
2025-07-29 17:45:30 - INFO - [生成摘要报告] 步骤 6: 分析文件分布
2025-07-29 17:45:30 - INFO - [生成摘要报告] 步骤 7: 分析异常关键词
2025-07-29 17:45:30 - INFO - ✓ 摘要报告生成完成
2025-07-29 17:45:30 - INFO -   分析日期: 2025-07-29
2025-07-29 17:45:30 - INFO -   总记录数: 343
2025-07-29 17:45:30 - INFO -   涉及Package: 37
2025-07-29 17:45:30 - INFO -   涉及团队: 8
2025-07-29 17:45:30 - INFO - 操作完成: 生成摘要报告 - 摘要报告生成完成: 343 条记录 (耗时: 0.013s, 步骤数: 7)
2025-07-29 17:45:30 - INFO - 摘要报告生成成功
2025-07-29 17:45:30 - INFO - [AIMonkey处理流程: 2025-07-29] 步骤 6: 摘要报告生成成功
2025-07-29 17:45:30 - INFO - 
步骤4: 导出分析结果...
2025-07-29 17:45:30 - INFO - [AIMonkey处理流程: 2025-07-29] 步骤 7: 导出分析结果
2025-07-29 17:45:30 - INFO - 开始操作: 导出分析结果
2025-07-29 17:45:30 - INFO - 开始导出分析结果到: D:\Monkey\aimonkey_analysis_results_20250729.xlsx
2025-07-29 17:45:30 - INFO - [导出分析结果] 步骤 1: 准备导出到: aimonkey_analysis_results_20250729.xlsx
2025-07-29 17:45:30 - INFO - [导出分析结果] 步骤 2: 转换数据: 343 条记录
2025-07-29 17:45:30 - INFO - [导出分析结果] 步骤 3: 写入主数据表
2025-07-29 17:45:30 - INFO - [导出分析结果] 步骤 4: 写入统计信息表
2025-07-29 17:45:30 - INFO - [导出分析结果] 步骤 5: 写入Package分布表
2025-07-29 17:45:30 - INFO - [导出分析结果] 步骤 6: 写入团队分布表
2025-07-29 17:45:30 - INFO - [导出分析结果] 步骤 7: 写入文件分布表
2025-07-29 17:45:30 - INFO - ✓ 成功导出分析结果
2025-07-29 17:45:30 - INFO -   文件路径: D:\Monkey\aimonkey_analysis_results_20250729.xlsx
2025-07-29 17:45:30 - INFO -   文件大小: 38.0 KB
2025-07-29 17:45:30 - INFO -   包含工作表: 分析结果, 统计信息, Package分布, 团队分布, 文件分布
2025-07-29 17:45:30 - INFO - 文件操作: 导出 - D:\Monkey\aimonkey_analysis_results_20250729.xlsx - 成功 - 大小: 38.0 KB
2025-07-29 17:45:30 - INFO - 操作完成: 导出分析结果 - 导出成功: 38.0 KB (耗时: 0.056s, 步骤数: 7)
2025-07-29 17:45:30 - INFO - 分析结果已导出到: aimonkey_analysis_results_20250729.xlsx
2025-07-29 17:45:30 - INFO - [AIMonkey处理流程: 2025-07-29] 步骤 8: 导出成功: aimonkey_analysis_results_20250729.xlsx
2025-07-29 17:45:30 - INFO - 
步骤5: 按owner分组异常信息...
2025-07-29 17:45:30 - INFO - [AIMonkey处理流程: 2025-07-29] 步骤 9: 按owner分组异常信息
2025-07-29 17:45:30 - INFO - 异常信息已按 8 个团队分组
2025-07-29 17:45:30 - INFO - [AIMonkey处理流程: 2025-07-29] 步骤 10: 分组完成: 8 个团队
2025-07-29 17:45:30 - INFO - 
步骤6: 发送飞书通知...
2025-07-29 17:45:30 - INFO - [AIMonkey处理流程: 2025-07-29] 步骤 11: 发送飞书通知
2025-07-29 17:45:30 - INFO - 开始操作: 发送飞书通知
2025-07-29 17:45:30 - INFO - 准备发送通知给 8 个团队
2025-07-29 17:45:30 - INFO - [发送飞书通知] 步骤 1: 准备发送 8 个通知
2025-07-29 17:45:30 - INFO - 发送通知给: 系统设置团队 (84 条异常)
2025-07-29 17:45:30 - INFO - [发送飞书通知] 步骤 2: 发送通知给: 系统设置团队
2025-07-29 17:45:31 - INFO -   ✓ 通知发送成功: None
2025-07-29 17:45:31 - INFO - [发送飞书通知] 步骤 3: 通知发送成功: 系统设置团队
2025-07-29 17:45:31 - INFO - 跳过未知团队 (204 条记录)
2025-07-29 17:45:31 - INFO - 发送通知给: 相机团队 (1 条异常)
2025-07-29 17:45:31 - INFO - [发送飞书通知] 步骤 4: 发送通知给: 相机团队
2025-07-29 17:45:32 - INFO -   ✓ 通知发送成功: None
2025-07-29 17:45:32 - INFO - [发送飞书通知] 步骤 5: 通知发送成功: 相机团队
2025-07-29 17:45:32 - INFO - 发送通知给: 系统UI团队 (7 条异常)
2025-07-29 17:45:32 - INFO - [发送飞书通知] 步骤 6: 发送通知给: 系统UI团队
2025-07-29 17:45:33 - INFO -   ✓ 通知发送成功: None
2025-07-29 17:45:33 - INFO - [发送飞书通知] 步骤 7: 通知发送成功: 系统UI团队
2025-07-29 17:45:33 - INFO - 发送通知给: 浏览器团队 (29 条异常)
2025-07-29 17:45:33 - INFO - [发送飞书通知] 步骤 8: 发送通知给: 浏览器团队
2025-07-29 17:45:34 - INFO -   ✓ 通知发送成功: None
2025-07-29 17:45:34 - INFO - [发送飞书通知] 步骤 9: 通知发送成功: 浏览器团队
2025-07-29 17:45:34 - INFO - 发送通知给: 电话团队 (13 条异常)
2025-07-29 17:45:34 - INFO - [发送飞书通知] 步骤 10: 发送通知给: 电话团队
2025-07-29 17:45:34 - INFO -   ✓ 通知发送成功: None
2025-07-29 17:45:34 - INFO - [发送飞书通知] 步骤 11: 通知发送成功: 电话团队
2025-07-29 17:45:34 - INFO - 发送通知给: Google服务团队 (2 条异常)
2025-07-29 17:45:34 - INFO - [发送飞书通知] 步骤 12: 发送通知给: Google服务团队
2025-07-29 17:45:35 - INFO -   ✓ 通知发送成功: None
2025-07-29 17:45:35 - INFO - [发送飞书通知] 步骤 13: 通知发送成功: Google服务团队
2025-07-29 17:45:35 - INFO - 发送通知给: 输入法团队 (3 条异常)
2025-07-29 17:45:35 - INFO - [发送飞书通知] 步骤 14: 发送通知给: 输入法团队
2025-07-29 17:45:35 - INFO -   ✓ 通知发送成功: None
2025-07-29 17:45:35 - INFO - [发送飞书通知] 步骤 15: 通知发送成功: 输入法团队
2025-07-29 17:45:35 - INFO - 
通知发送统计:
2025-07-29 17:45:35 - INFO -   成功发送: 7 条
2025-07-29 17:45:35 - INFO -   发送失败: 0 条
2025-07-29 17:45:35 - INFO - 批量通知发送: 总计 8, 成功 7, 失败 0
2025-07-29 17:45:35 - INFO - 操作完成: 发送飞书通知 - 通知发送完成: 成功 7/8 条 (耗时: 5.581s, 步骤数: 15)
2025-07-29 17:45:35 - INFO - 成功发送 7 条通知
2025-07-29 17:45:35 - INFO - [AIMonkey处理流程: 2025-07-29] 步骤 12: 通知发送成功: 7 条
2025-07-29 17:45:35 - INFO - 
================================================================================
2025-07-29 17:45:35 - INFO - 处理流程完成统计
2025-07-29 17:45:35 - INFO - ================================================================================
2025-07-29 17:45:35 - INFO - ✓ 目标日期: 2025-07-29
2025-07-29 17:45:35 - INFO - ✓ 找到文件: 15 个
2025-07-29 17:45:35 - INFO - ✓ 分析文件: 15 个
2025-07-29 17:45:35 - INFO - ✓ 分析记录: 343 条
2025-07-29 17:45:35 - INFO - ✓ 涉及Package: 37 个
2025-07-29 17:45:35 - INFO - ✓ 涉及团队: 8 个
2025-07-29 17:45:35 - INFO - ✓ 发送通知: 7 条
2025-07-29 17:45:35 - INFO - ✓ 导出文件: aimonkey_analysis_results_20250729.xlsx
2025-07-29 17:45:35 - INFO - ================================================================================
2025-07-29 17:45:35 - INFO - AIMonkey异常信息处理流程完成
2025-07-29 17:45:35 - INFO - ================================================================================
2025-07-29 17:45:35 - INFO - 操作完成: AIMonkey处理流程: 2025-07-29 - 处理流程完成: 343 条记录, 7 条通知 (耗时: 8.856s, 步骤数: 12)
2025-07-29 17:47:54 - INFO - AIMonkey工具初始化完成
2025-07-29 17:47:54 - INFO - 重试配置: {'max_retries': 1, 'retry_delay': 2}
2025-07-29 17:47:54 - INFO - 开始操作: AIMonkey处理流程: 2025-07-28
2025-07-29 17:47:54 - INFO - ================================================================================
2025-07-29 17:47:54 - INFO - 开始AIMonkey异常信息处理流程 (目标日期: 2025-07-28)
2025-07-29 17:47:54 - INFO - ================================================================================
2025-07-29 17:47:54 - INFO - 
步骤1: 下载AIMonkey文件...
2025-07-29 17:47:54 - INFO - [AIMonkey处理流程: 2025-07-28] 步骤 1: 开始下载文件
2025-07-29 17:47:54 - INFO - 开始操作: AIMonkey文件下载
2025-07-29 17:47:54 - INFO - ============================================================
2025-07-29 17:47:54 - INFO - 开始下载AIMonkey文件
2025-07-29 17:47:54 - INFO - ============================================================
2025-07-29 17:47:55 - INFO - SMB会话已建立: 10.205.101.200
2025-07-29 17:47:55 - INFO - 目标日期: 2025-07-28
2025-07-29 17:47:55 - INFO - 本地基础目录: D:\Monkey
2025-07-29 17:47:55 - INFO - [AIMonkey文件下载] 步骤 1: 设置目标日期: 2025-07-28
2025-07-29 17:47:55 - INFO - 
[1/2] 处理目录: tOS15.1
2025-07-29 17:47:55 - INFO - 远程路径: tOS15.1\AIMonkey\2025-07-28
2025-07-29 17:47:55 - INFO - 本地路径: D:\Monkey\tOS15.1\AIMonkey\2025-07-28
2025-07-29 17:47:55 - INFO - [AIMonkey文件下载] 步骤 2: 处理目录 tOS15.1
2025-07-29 17:47:55 - INFO - 开始操作: SMB文件下载: tOS15.1\AIMonkey\2025-07-28
2025-07-29 17:47:55 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-28] 步骤 1: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-07-28
2025-07-29 17:47:55 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-28] 步骤 2: 连接远程目录: \\10.205.101.200\osptlog1\tOS15.1\AIMonkey\2025-07-28
2025-07-29 17:47:55 - INFO - 找到 9 个文件/目录
2025-07-29 17:47:55 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-28] 步骤 3: 扫描到 9 个文件/目录
2025-07-29 17:47:55 - INFO - file_name: 系统应用
2025-07-29 17:47:55 - INFO - file_name: 桌面
2025-07-29 17:47:55 - INFO - file_name: 门户
2025-07-29 17:47:55 - INFO - file_name: 框架
2025-07-29 17:47:55 - INFO - file_name: 基础服务
2025-07-29 17:47:55 - INFO - file_name: 敏捷
2025-07-29 17:47:55 - INFO - file_name: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xls
2025-07-29 17:47:55 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-28] 步骤 4: 准备下载到本地: D:\Monkey\tOS15.1\AIMonkey\2025-07-28\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx
2025-07-29 17:47:55 - INFO - 开始操作: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xls
2025-07-29 17:47:55 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xls] 步骤 1: 开始下载文件
2025-07-29 17:47:55 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-07-28
2025-07-29 17:47:55 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xls] 步骤 3: 获取文件大小: 392.5 KB
2025-07-29 17:47:55 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xls] 步骤 4: 开始文件传输
2025-07-29 17:47:56 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xls] 步骤 5: 文件传输完成
2025-07-29 17:47:56 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xls] 步骤 6: 下载完成: 392.5 KB
2025-07-29 17:47:56 - INFO - 文件操作: 下载 - D:\Monkey\tOS15.1\AIMonkey\2025-07-28\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx - 成功 - 大小: 392.5 KB
2025-07-29 17:47:56 - INFO - 操作完成: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xls - 文件下载成功: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xls (耗时: 0.816s, 步骤数: 6)
2025-07-29 17:47:56 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-28] 步骤 5: 成功下载: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xls
2025-07-29 17:47:56 - INFO - file_name: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xls
2025-07-29 17:47:56 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-28] 步骤 6: 准备下载到本地: D:\Monkey\tOS15.1\AIMonkey\2025-07-28\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx
2025-07-29 17:47:56 - INFO - 开始操作: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xls
2025-07-29 17:47:56 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xls] 步骤 1: 开始下载文件
2025-07-29 17:47:56 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-07-28
2025-07-29 17:47:56 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xls] 步骤 3: 获取文件大小: 17.5 KB
2025-07-29 17:47:56 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xls] 步骤 4: 开始文件传输
2025-07-29 17:47:56 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xls] 步骤 5: 文件传输完成
2025-07-29 17:47:56 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xls] 步骤 6: 下载完成: 17.5 KB
2025-07-29 17:47:56 - INFO - 文件操作: 下载 - D:\Monkey\tOS15.1\AIMonkey\2025-07-28\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx - 成功 - 大小: 17.5 KB
2025-07-29 17:47:56 - INFO - 操作完成: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xls - 文件下载成功: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xls (耗时: 0.170s, 步骤数: 6)
2025-07-29 17:47:56 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-28] 步骤 7: 成功下载: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xls
2025-07-29 17:47:56 - INFO - file_name: statistics.txt
2025-07-29 17:47:56 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-28] 步骤 8: 准备下载到本地: D:\Monkey\tOS15.1\AIMonkey\2025-07-28\statistics.txt
2025-07-29 17:47:56 - INFO - 开始操作: 下载文件: statistics.txt
2025-07-29 17:47:56 - INFO - [下载文件: statistics.txt] 步骤 1: 开始下载文件
2025-07-29 17:47:56 - INFO - [下载文件: statistics.txt] 步骤 2: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-07-28
2025-07-29 17:47:56 - INFO - [下载文件: statistics.txt] 步骤 3: 获取文件大小: 354.0 B
2025-07-29 17:47:56 - INFO - [下载文件: statistics.txt] 步骤 4: 开始文件传输
2025-07-29 17:47:56 - INFO - [下载文件: statistics.txt] 步骤 5: 文件传输完成
2025-07-29 17:47:56 - INFO - [下载文件: statistics.txt] 步骤 6: 下载完成: 354.0 B
2025-07-29 17:47:56 - INFO - 文件操作: 下载 - D:\Monkey\tOS15.1\AIMonkey\2025-07-28\statistics.txt - 成功 - 大小: 354.0 B
2025-07-29 17:47:56 - INFO - 操作完成: 下载文件: statistics.txt - 文件下载成功: statistics.txt (耗时: 0.132s, 步骤数: 6)
2025-07-29 17:47:56 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-28] 步骤 9: 成功下载: statistics.txt
2025-07-29 17:47:56 - INFO - 操作完成: SMB文件下载: tOS15.1\AIMonkey\2025-07-28 - 下载完成，成功下载 3 个文件 (耗时: 1.598s, 步骤数: 9)
2025-07-29 17:47:56 - INFO - ✓ 成功下载 3 个文件
2025-07-29 17:47:56 - INFO - [AIMonkey文件下载] 步骤 3: 成功下载 3 个文件
2025-07-29 17:47:56 - INFO - 
[2/2] 处理目录: tOS16.0
2025-07-29 17:47:56 - INFO - 远程路径: tOS16.0\AIMonkey\2025-07-28
2025-07-29 17:47:56 - INFO - 本地路径: D:\Monkey\tOS16.0\AIMonkey\2025-07-28
2025-07-29 17:47:56 - INFO - [AIMonkey文件下载] 步骤 4: 处理目录 tOS16.0
2025-07-29 17:47:56 - INFO - 开始操作: SMB文件下载: tOS16.0\AIMonkey\2025-07-28
2025-07-29 17:47:56 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 1: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-07-28
2025-07-29 17:47:56 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 2: 连接远程目录: \\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28
2025-07-29 17:47:56 - INFO - 找到 10 个文件/目录
2025-07-29 17:47:56 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 3: 扫描到 10 个文件/目录
2025-07-29 17:47:56 - INFO - file_name: 门户
2025-07-29 17:47:56 - INFO - file_name: 创新产品
2025-07-29 17:47:56 - INFO - file_name: 基础服务
2025-07-29 17:47:56 - INFO - file_name: 桌面
2025-07-29 17:47:56 - INFO - file_name: 框架
2025-07-29 17:47:56 - INFO - file_name: 系统应用
2025-07-29 17:47:56 - INFO - file_name: 敏捷
2025-07-29 17:47:56 - INFO - file_name: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls
2025-07-29 17:47:56 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 4: 准备下载到本地: D:\Monkey\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xlsx
2025-07-29 17:47:56 - INFO - 开始操作: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls
2025-07-29 17:47:56 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls] 步骤 1: 开始下载文件
2025-07-29 17:47:56 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-07-28
2025-07-29 17:47:56 - WARNING - 下载异常，等待 2 秒后重试: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls - [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls'
2025-07-29 17:47:56 - WARNING - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls] 步骤 3: 下载异常，等待重试: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls'
2025-07-29 17:47:56 - INFO - 第 2 次尝试下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls
2025-07-29 17:47:56 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls] 步骤 4: 重试下载 (第 2 次)
2025-07-29 17:47:58 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls] 步骤 5: 开始下载文件
2025-07-29 17:47:58 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls] 步骤 6: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-07-28
2025-07-29 17:47:58 - ERROR - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls'
2025-07-29 17:47:58 - ERROR - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls' (耗时: 2.092s, 步骤数: 6)
2025-07-29 17:47:58 - ERROR - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls
2025-07-29 17:47:58 - ERROR - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 5: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls
2025-07-29 17:47:58 - INFO - file_name: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls
2025-07-29 17:47:58 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 6: 准备下载到本地: D:\Monkey\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xlsx
2025-07-29 17:47:58 - INFO - 开始操作: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls
2025-07-29 17:47:58 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls] 步骤 1: 开始下载文件
2025-07-29 17:47:58 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-07-28
2025-07-29 17:47:58 - WARNING - 下载异常，等待 2 秒后重试: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls - [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls'
2025-07-29 17:47:58 - WARNING - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls] 步骤 3: 下载异常，等待重试: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls'
2025-07-29 17:47:58 - INFO - 第 2 次尝试下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls
2025-07-29 17:47:58 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls] 步骤 4: 重试下载 (第 2 次)
2025-07-29 17:48:00 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls] 步骤 5: 开始下载文件
2025-07-29 17:48:00 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls] 步骤 6: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-07-28
2025-07-29 17:48:00 - ERROR - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls'
2025-07-29 17:48:00 - ERROR - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls' (耗时: 2.090s, 步骤数: 6)
2025-07-29 17:48:00 - ERROR - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls
2025-07-29 17:48:00 - ERROR - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 7: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls
2025-07-29 17:48:00 - INFO - file_name: statistics.txt
2025-07-29 17:48:00 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 8: 准备下载到本地: D:\Monkey\tOS16.0\AIMonkey\2025-07-28\statistics.txt
2025-07-29 17:48:00 - INFO - 开始操作: 下载文件: statistics.txt
2025-07-29 17:48:00 - INFO - [下载文件: statistics.txt] 步骤 1: 开始下载文件
2025-07-29 17:48:00 - INFO - [下载文件: statistics.txt] 步骤 2: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-07-28
2025-07-29 17:48:01 - INFO - [下载文件: statistics.txt] 步骤 3: 获取文件大小: 374.0 B
2025-07-29 17:48:01 - INFO - [下载文件: statistics.txt] 步骤 4: 开始文件传输
2025-07-29 17:48:01 - INFO - [下载文件: statistics.txt] 步骤 5: 文件传输完成
2025-07-29 17:48:01 - INFO - [下载文件: statistics.txt] 步骤 6: 下载完成: 374.0 B
2025-07-29 17:48:01 - INFO - 文件操作: 下载 - D:\Monkey\tOS16.0\AIMonkey\2025-07-28\statistics.txt - 成功 - 大小: 374.0 B
2025-07-29 17:48:01 - INFO - 操作完成: 下载文件: statistics.txt - 文件下载成功: statistics.txt (耗时: 0.119s, 步骤数: 6)
2025-07-29 17:48:01 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 9: 成功下载: statistics.txt
2025-07-29 17:48:01 - INFO - 操作完成: SMB文件下载: tOS16.0\AIMonkey\2025-07-28 - 下载完成，成功下载 1 个文件 (耗时: 4.466s, 步骤数: 9)
2025-07-29 17:48:01 - INFO - ✓ 成功下载 1 个文件
2025-07-29 17:48:01 - INFO - [AIMonkey文件下载] 步骤 5: 成功下载 1 个文件
2025-07-29 17:48:01 - INFO - 批量文件下载: 总计 2, 成功 4, 失败 0
2025-07-29 17:48:01 - INFO - 
============================================================
2025-07-29 17:48:01 - INFO - 下载完成统计
2025-07-29 17:48:01 - INFO - ============================================================
2025-07-29 17:48:01 - INFO - 总共下载: 4 个文件
2025-07-29 17:48:01 - INFO - 下载失败: 0 个文件
2025-07-29 17:48:01 - INFO - 目标目录数: 2
2025-07-29 17:48:01 - INFO - 
下载的文件列表:
2025-07-29 17:48:01 - INFO -   - Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx (392.5 KB)
2025-07-29 17:48:01 - INFO -   - Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx (17.5 KB)
2025-07-29 17:48:01 - INFO -   - statistics.txt (354.0 B)
2025-07-29 17:48:01 - INFO -   - statistics.txt (374.0 B)
2025-07-29 17:48:01 - INFO - 操作完成: AIMonkey文件下载 - 下载完成，总计 4 个文件 (耗时: 6.401s, 步骤数: 5)
2025-07-29 17:48:01 - INFO - [AIMonkey处理流程: 2025-07-28] 步骤 2: 验证下载文件
2025-07-29 17:48:01 - INFO - 开始操作: 文件验证
2025-07-29 17:48:01 - INFO - [文件验证] 步骤 1: 开始验证 4 个文件
2025-07-29 17:48:01 - INFO - ✓ Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx - 392.5 KB
2025-07-29 17:48:01 - INFO - ✓ Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx - 17.5 KB
2025-07-29 17:48:01 - INFO - ✓ statistics.txt - 354.0 B
2025-07-29 17:48:01 - INFO - ✓ statistics.txt - 374.0 B
2025-07-29 17:48:01 - INFO - 
验证结果:
2025-07-29 17:48:01 - INFO -   总文件数: 4
2025-07-29 17:48:01 - INFO -   有效文件: 4
2025-07-29 17:48:01 - INFO -   无效文件: 0
2025-07-29 17:48:01 - INFO -   缺失文件: 0
2025-07-29 17:48:01 - INFO - 操作完成: 文件验证 - 验证完成: 有效 4/4 个文件 (耗时: 0.006s, 步骤数: 1)
2025-07-29 17:48:01 - INFO - 
步骤2: 智能搜索和分析Excel文件...
2025-07-29 17:48:01 - INFO - [AIMonkey处理流程: 2025-07-28] 步骤 3: 开始智能文件搜索和分析
2025-07-29 17:48:01 - INFO - 开始操作: 搜索Excel文件: AIMonkey
2025-07-29 17:48:01 - INFO - [搜索Excel文件: AIMonkey] 步骤 1: 搜索日期后缀: 20250728
2025-07-29 17:48:01 - INFO - 搜索条件:
2025-07-29 17:48:01 - INFO -   目录: D:\Monkey\tOS15.1\AIMonkey
2025-07-29 17:48:01 - INFO -   日期后缀: 20250728
2025-07-29 17:48:01 - INFO -   关键词: ['MonkeyAEE', 'Result', 'AIMonkey']
2025-07-29 17:48:01 - INFO - [搜索Excel文件: AIMonkey] 步骤 2: 设置搜索条件: 日期=20250728, 关键词=['MonkeyAEE', 'Result', 'AIMonkey']
2025-07-29 17:48:01 - INFO - ✓ 找到匹配文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx
2025-07-29 17:48:01 - INFO - ✓ 找到匹配文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx
2025-07-29 17:48:01 - INFO - 在 D:\Monkey\tOS15.1\AIMonkey 中找到 2 个匹配的Excel文件
2025-07-29 17:48:01 - INFO - 找到的文件列表:
2025-07-29 17:48:01 - INFO -   1. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx (17.5 KB)
2025-07-29 17:48:01 - INFO -   2. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx (392.5 KB)
2025-07-29 17:48:01 - INFO - 操作完成: 搜索Excel文件: AIMonkey - 找到 2 个匹配文件 (耗时: 0.007s, 步骤数: 2)
2025-07-29 17:48:01 - INFO - 开始操作: 关键词搜索: Result, MonkeyAEE, SH
2025-07-29 17:48:01 - INFO - 关键词搜索配置:
2025-07-29 17:48:01 - INFO -   目录: D:\Monkey\tOS15.1\AIMonkey
2025-07-29 17:48:01 - INFO -   关键词: ['Result', 'MonkeyAEE', 'SH']
2025-07-29 17:48:01 - INFO -   扩展名: ['.xls', '.xlsx']
2025-07-29 17:48:01 - INFO -   日期模式: ['20250728', '2025_07_28', '2025.07.28']
2025-07-29 17:48:01 - INFO - [关键词搜索: Result, MonkeyAEE, SH] 步骤 1: 搜索关键词: ['Result', 'MonkeyAEE', 'SH'], 扩展名: ['.xls', '.xlsx']
2025-07-29 17:48:01 - INFO - 在 D:\Monkey\tOS15.1\AIMonkey 中找到 8 个匹配的文件
2025-07-29 17:48:01 - INFO - 找到的文件（按匹配度排序）:
2025-07-29 17:48:01 - INFO -   1. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx (分数: 4)
2025-07-29 17:48:01 - INFO -      大小: 17.5 KB
2025-07-29 17:48:01 - INFO -      匹配: 关键词: Result, 关键词: SH, 日期: 20250728
2025-07-29 17:48:01 - INFO -   2. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx (分数: 4)
2025-07-29 17:48:01 - INFO -      大小: 392.5 KB
2025-07-29 17:48:01 - INFO -      匹配: 关键词: Result, 关键词: SH, 日期: 20250728
2025-07-29 17:48:01 - INFO -   3. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls (分数: 2)
2025-07-29 17:48:01 - INFO -      大小: 41.5 KB
2025-07-29 17:48:01 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:48:01 - INFO -   4. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls (分数: 2)
2025-07-29 17:48:01 - INFO -      大小: 150.5 KB
2025-07-29 17:48:01 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:48:01 - INFO -   5. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx (分数: 2)
2025-07-29 17:48:01 - INFO -      大小: 17.5 KB
2025-07-29 17:48:01 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:48:01 - INFO -   6. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_corrected.xls (分数: 2)
2025-07-29 17:48:01 - INFO -      大小: 17.5 KB
2025-07-29 17:48:01 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:48:01 - INFO -   7. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx (分数: 2)
2025-07-29 17:48:01 - INFO -      大小: 432.5 KB
2025-07-29 17:48:01 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:48:01 - INFO -   8. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls (分数: 2)
2025-07-29 17:48:01 - INFO -      大小: 432.5 KB
2025-07-29 17:48:01 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:48:01 - INFO - 操作完成: 关键词搜索: Result, MonkeyAEE, SH - 找到 8 个匹配文件 (耗时: 0.014s, 步骤数: 1)
2025-07-29 17:48:01 - INFO - 开始操作: 搜索Excel文件: AIMonkey
2025-07-29 17:48:01 - INFO - [搜索Excel文件: AIMonkey] 步骤 1: 搜索日期后缀: 20250728
2025-07-29 17:48:01 - INFO - 搜索条件:
2025-07-29 17:48:01 - INFO -   目录: D:\Monkey\tOS16.0\AIMonkey
2025-07-29 17:48:01 - INFO -   日期后缀: 20250728
2025-07-29 17:48:01 - INFO -   关键词: ['MonkeyAEE', 'Result', 'AIMonkey']
2025-07-29 17:48:01 - INFO - [搜索Excel文件: AIMonkey] 步骤 2: 设置搜索条件: 日期=20250728, 关键词=['MonkeyAEE', 'Result', 'AIMonkey']
2025-07-29 17:48:01 - INFO - 在 D:\Monkey\tOS16.0\AIMonkey 中找到 0 个匹配的Excel文件
2025-07-29 17:48:01 - INFO - 操作完成: 搜索Excel文件: AIMonkey - 找到 0 个匹配文件 (耗时: 0.004s, 步骤数: 2)
2025-07-29 17:48:01 - INFO - 开始操作: 关键词搜索: Result, MonkeyAEE, SH
2025-07-29 17:48:01 - INFO - 关键词搜索配置:
2025-07-29 17:48:01 - INFO -   目录: D:\Monkey\tOS16.0\AIMonkey
2025-07-29 17:48:01 - INFO -   关键词: ['Result', 'MonkeyAEE', 'SH']
2025-07-29 17:48:01 - INFO -   扩展名: ['.xls', '.xlsx']
2025-07-29 17:48:01 - INFO -   日期模式: ['20250728', '2025_07_28', '2025.07.28']
2025-07-29 17:48:01 - INFO - [关键词搜索: Result, MonkeyAEE, SH] 步骤 1: 搜索关键词: ['Result', 'MonkeyAEE', 'SH'], 扩展名: ['.xls', '.xlsx']
2025-07-29 17:48:01 - INFO - 在 D:\Monkey\tOS16.0\AIMonkey 中找到 11 个匹配的文件
2025-07-29 17:48:01 - INFO - 找到的文件（按匹配度排序）:
2025-07-29 17:48:01 - INFO -   1. Result_None_None_MonkeyAEE_SH_20250617.xlsx (分数: 3)
2025-07-29 17:48:01 - INFO -      大小: 29.5 KB
2025-07-29 17:48:01 - INFO -      匹配: 关键词: Result, 关键词: MonkeyAEE, 关键词: SH
2025-07-29 17:48:01 - INFO -   2. Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx (分数: 3)
2025-07-29 17:48:01 - INFO -      大小: 17.5 KB
2025-07-29 17:48:01 - INFO -      匹配: 关键词: Result, 关键词: MonkeyAEE, 关键词: SH
2025-07-29 17:48:01 - INFO -   3. Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls (分数: 3)
2025-07-29 17:48:01 - INFO -      大小: 17.5 KB
2025-07-29 17:48:01 - INFO -      匹配: 关键词: Result, 关键词: MonkeyAEE, 关键词: SH
2025-07-29 17:48:01 - INFO -   4. Result_None_None_MonkeyAEE_SH_20250617_corrected.xls (分数: 3)
2025-07-29 17:48:01 - INFO -      大小: 29.5 KB
2025-07-29 17:48:01 - INFO -      匹配: 关键词: Result, 关键词: MonkeyAEE, 关键词: SH
2025-07-29 17:48:01 - INFO -   5. Result_None_None_MonkeyAEE_SH_20250617_org.xlsx (分数: 3)
2025-07-29 17:48:01 - INFO -      大小: 21.5 KB
2025-07-29 17:48:01 - INFO -      匹配: 关键词: Result, 关键词: MonkeyAEE, 关键词: SH
2025-07-29 17:48:01 - INFO -   6. Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls (分数: 3)
2025-07-29 17:48:01 - INFO -      大小: 21.5 KB
2025-07-29 17:48:01 - INFO -      匹配: 关键词: Result, 关键词: MonkeyAEE, 关键词: SH
2025-07-29 17:48:01 - INFO -   7. Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls (分数: 2)
2025-07-29 17:48:01 - INFO -      大小: 158.5 KB
2025-07-29 17:48:01 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:48:01 - INFO -   8. Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx (分数: 2)
2025-07-29 17:48:01 - INFO -      大小: 182.5 KB
2025-07-29 17:48:01 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:48:01 - INFO -   9. Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_corrected.xls (分数: 2)
2025-07-29 17:48:01 - INFO -      大小: 182.5 KB
2025-07-29 17:48:01 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:48:01 - INFO -   10. Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx (分数: 2)
2025-07-29 17:48:01 - INFO -      大小: 457.0 KB
2025-07-29 17:48:01 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:48:01 - INFO -   11. Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls (分数: 2)
2025-07-29 17:48:01 - INFO -      大小: 457.0 KB
2025-07-29 17:48:01 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:48:01 - INFO - 操作完成: 关键词搜索: Result, MonkeyAEE, SH - 找到 11 个匹配文件 (耗时: 0.018s, 步骤数: 1)
2025-07-29 17:48:01 - INFO - 找到 21 个目标Excel文件，开始分析...
2025-07-29 17:48:01 - INFO - 开始操作: 批量Excel分析
2025-07-29 17:48:01 - INFO - 开始批量分析 21 个Excel文件
2025-07-29 17:48:01 - INFO - ============================================================
2025-07-29 17:48:01 - INFO - [批量Excel分析] 步骤 1: 开始批量分析 21 个文件
2025-07-29 17:48:01 - INFO - [批量Excel分析] 步骤 2: 预加载Package-Owner映射
2025-07-29 17:48:01 - INFO - 
[1/21] 处理文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx
2025-07-29 17:48:01 - INFO - 开始操作: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx
2025-07-29 17:48:01 - INFO - 开始分析Excel文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx] 步骤 1: 开始分析文件
2025-07-29 17:48:01 - INFO -   检测文件格式...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx] 步骤 2: 检测文件格式
2025-07-29 17:48:01 - INFO -   文件扩展名: .xlsx
2025-07-29 17:48:01 - INFO -   实际格式: xls
2025-07-29 17:48:01 - INFO -   ✓ 创建格式正确的文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_corrected.xls
2025-07-29 17:48:01 - INFO -   使用修复后的文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_corrected.xls
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx] 步骤 3: 使用修复后的文件
2025-07-29 17:48:01 - INFO -   使用引擎: xlrd
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx] 步骤 4: 使用 xlrd 引擎读取
2025-07-29 17:48:01 - INFO -   文件读取成功，共 2 行 14 列
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx] 步骤 5: 文件读取成功: 2 行 14 列
2025-07-29 17:48:01 - INFO -   找到Package列: 'Package'
2025-07-29 17:48:01 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:48:01 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:48:01 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:48:01 - INFO -   列名识别结果:
2025-07-29 17:48:01 - INFO -     Package列: Package
2025-07-29 17:48:01 - INFO -     CausedBy列: CausedBy
2025-07-29 17:48:01 - INFO -     Version列: Version
2025-07-29 17:48:01 - INFO -     Path列: Path
2025-07-29 17:48:01 - INFO -   开始数据预处理...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx] 步骤 6: 开始数据预处理
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx] 步骤 7: 加载Package-Owner映射
2025-07-29 17:48:01 - INFO -   开始批量处理 2 行数据...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx] 步骤 8: 开始批量处理 2 行数据
2025-07-29 17:48:01 - INFO -   分析完成:
2025-07-29 17:48:01 - INFO -     总记录数: 2
2025-07-29 17:48:01 - INFO -     唯一Package数: 2
2025-07-29 17:48:01 - INFO -     涉及团队数: 1
2025-07-29 17:48:01 - INFO -     未知团队记录: 2
2025-07-29 17:48:01 - INFO -     Top 5 Package:
2025-07-29 17:48:01 - INFO -       com.gallery20: 1 条记录
2025-07-29 17:48:01 - INFO -       /vendor/bin/hw/android.hardware.audio.service.mediatek: 1 条记录
2025-07-29 17:48:01 - INFO - Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_corrected.xls - 成功 - 2 条记录
2025-07-29 17:48:01 - INFO - 操作完成: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx - 分析完成: 2 条记录 (耗时: 0.031s, 步骤数: 8)
2025-07-29 17:48:01 - INFO -   ✓ 成功分析，获得 2 条记录
2025-07-29 17:48:01 - INFO - 
[2/21] 处理文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx
2025-07-29 17:48:01 - INFO - 开始操作: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx
2025-07-29 17:48:01 - INFO - 开始分析Excel文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 1: 开始分析文件
2025-07-29 17:48:01 - INFO -   检测文件格式...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 2: 检测文件格式
2025-07-29 17:48:01 - INFO -   文件扩展名: .xlsx
2025-07-29 17:48:01 - INFO -   实际格式: xls
2025-07-29 17:48:01 - INFO -   ✓ 创建格式正确的文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_corrected.xls
2025-07-29 17:48:01 - INFO -   使用修复后的文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_corrected.xls
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 3: 使用修复后的文件
2025-07-29 17:48:01 - INFO -   使用引擎: xlrd
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 4: 使用 xlrd 引擎读取
2025-07-29 17:48:01 - INFO -   文件读取成功，共 4 行 14 列
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 5: 文件读取成功: 4 行 14 列
2025-07-29 17:48:01 - INFO -   找到Package列: 'Package'
2025-07-29 17:48:01 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:48:01 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:48:01 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:48:01 - INFO -   列名识别结果:
2025-07-29 17:48:01 - INFO -     Package列: Package
2025-07-29 17:48:01 - INFO -     CausedBy列: CausedBy
2025-07-29 17:48:01 - INFO -     Version列: Version
2025-07-29 17:48:01 - INFO -     Path列: Path
2025-07-29 17:48:01 - INFO -   开始数据预处理...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 6: 开始数据预处理
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 7: 加载Package-Owner映射
2025-07-29 17:48:01 - INFO -   开始批量处理 4 行数据...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 8: 开始批量处理 4 行数据
2025-07-29 17:48:01 - INFO -   分析完成:
2025-07-29 17:48:01 - INFO -     总记录数: 4
2025-07-29 17:48:01 - INFO -     唯一Package数: 2
2025-07-29 17:48:01 - INFO -     涉及团队数: 1
2025-07-29 17:48:01 - INFO -     未知团队记录: 4
2025-07-29 17:48:01 - INFO -     Top 5 Package:
2025-07-29 17:48:01 - INFO -       com.transsion.airtransfer: 3 条记录
2025-07-29 17:48:01 - INFO -       com.transsion.cloudserver: 1 条记录
2025-07-29 17:48:01 - INFO - Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_corrected.xls - 成功 - 4 条记录
2025-07-29 17:48:01 - INFO - 操作完成: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx - 分析完成: 4 条记录 (耗时: 0.020s, 步骤数: 8)
2025-07-29 17:48:01 - INFO -   ✓ 成功分析，获得 4 条记录
2025-07-29 17:48:01 - INFO - 
[3/21] 处理文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx
2025-07-29 17:48:01 - INFO - 开始操作: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx
2025-07-29 17:48:01 - INFO - 开始分析Excel文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 1: 开始分析文件
2025-07-29 17:48:01 - INFO -   检测文件格式...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 2: 检测文件格式
2025-07-29 17:48:01 - INFO -   文件扩展名: .xlsx
2025-07-29 17:48:01 - INFO -   实际格式: xls
2025-07-29 17:48:01 - INFO -   ✓ 创建格式正确的文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls
2025-07-29 17:48:01 - INFO -   使用修复后的文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 3: 使用修复后的文件
2025-07-29 17:48:01 - INFO -   使用引擎: xlrd
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 4: 使用 xlrd 引擎读取
2025-07-29 17:48:01 - INFO -   文件读取成功，共 89 行 14 列
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 5: 文件读取成功: 89 行 14 列
2025-07-29 17:48:01 - INFO -   找到Package列: 'Package'
2025-07-29 17:48:01 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:48:01 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:48:01 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:48:01 - INFO -   列名识别结果:
2025-07-29 17:48:01 - INFO -     Package列: Package
2025-07-29 17:48:01 - INFO -     CausedBy列: CausedBy
2025-07-29 17:48:01 - INFO -     Version列: Version
2025-07-29 17:48:01 - INFO -     Path列: Path
2025-07-29 17:48:01 - INFO -   开始数据预处理...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 6: 开始数据预处理
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 7: 加载Package-Owner映射
2025-07-29 17:48:01 - INFO -   开始批量处理 89 行数据...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 8: 开始批量处理 89 行数据
2025-07-29 17:48:01 - INFO -   分析完成:
2025-07-29 17:48:01 - INFO -     总记录数: 89
2025-07-29 17:48:01 - INFO -     唯一Package数: 16
2025-07-29 17:48:01 - INFO -     涉及团队数: 7
2025-07-29 17:48:01 - INFO -     未知团队记录: 19
2025-07-29 17:48:01 - INFO -     Top 5 Package:
2025-07-29 17:48:01 - INFO -       com.android.settings: 46 条记录
2025-07-29 17:48:01 - INFO -       com.android.phone: 9 条记录
2025-07-29 17:48:01 - INFO -       com.android.chrome: 9 条记录
2025-07-29 17:48:01 - INFO -       com.android.settings.intelligence: 5 条记录
2025-07-29 17:48:01 - INFO -       com.transsion.launcher3: 4 条记录
2025-07-29 17:48:01 - INFO - Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls - 成功 - 89 条记录
2025-07-29 17:48:01 - INFO - 操作完成: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx - 分析完成: 89 条记录 (耗时: 0.030s, 步骤数: 8)
2025-07-29 17:48:01 - INFO -   ✓ 成功分析，获得 89 条记录
2025-07-29 17:48:01 - INFO - 
[4/21] 处理文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_corrected.xls
2025-07-29 17:48:01 - INFO - 开始操作: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_corrected.xls
2025-07-29 17:48:01 - INFO - 开始分析Excel文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_corrected.xls
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_corrected.xls] 步骤 1: 开始分析文件
2025-07-29 17:48:01 - INFO -   检测文件格式...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_corrected.xls] 步骤 2: 检测文件格式
2025-07-29 17:48:01 - INFO -   文件扩展名: .xls
2025-07-29 17:48:01 - INFO -   实际格式: xls
2025-07-29 17:48:01 - INFO -   使用引擎: xlrd
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_corrected.xls] 步骤 3: 使用 xlrd 引擎读取
2025-07-29 17:48:01 - INFO -   文件读取成功，共 4 行 14 列
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_corrected.xls] 步骤 4: 文件读取成功: 4 行 14 列
2025-07-29 17:48:01 - INFO -   找到Package列: 'Package'
2025-07-29 17:48:01 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:48:01 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:48:01 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:48:01 - INFO -   列名识别结果:
2025-07-29 17:48:01 - INFO -     Package列: Package
2025-07-29 17:48:01 - INFO -     CausedBy列: CausedBy
2025-07-29 17:48:01 - INFO -     Version列: Version
2025-07-29 17:48:01 - INFO -     Path列: Path
2025-07-29 17:48:01 - INFO -   开始数据预处理...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_corrected.xls] 步骤 5: 开始数据预处理
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_corrected.xls] 步骤 6: 加载Package-Owner映射
2025-07-29 17:48:01 - INFO -   开始批量处理 4 行数据...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_corrected.xls] 步骤 7: 开始批量处理 4 行数据
2025-07-29 17:48:01 - INFO -   分析完成:
2025-07-29 17:48:01 - INFO -     总记录数: 4
2025-07-29 17:48:01 - INFO -     唯一Package数: 2
2025-07-29 17:48:01 - INFO -     涉及团队数: 1
2025-07-29 17:48:01 - INFO -     未知团队记录: 4
2025-07-29 17:48:01 - INFO -     Top 5 Package:
2025-07-29 17:48:01 - INFO -       com.transsion.airtransfer: 3 条记录
2025-07-29 17:48:01 - INFO -       com.transsion.cloudserver: 1 条记录
2025-07-29 17:48:01 - INFO - Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_corrected.xls - 成功 - 4 条记录
2025-07-29 17:48:01 - INFO - 操作完成: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_corrected.xls - 分析完成: 4 条记录 (耗时: 0.017s, 步骤数: 7)
2025-07-29 17:48:01 - INFO -   ✓ 成功分析，获得 4 条记录
2025-07-29 17:48:01 - INFO - 
[5/21] 处理文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx
2025-07-29 17:48:01 - INFO - 开始操作: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx
2025-07-29 17:48:01 - INFO - 开始分析Excel文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx] 步骤 1: 开始分析文件
2025-07-29 17:48:01 - INFO -   检测文件格式...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx] 步骤 2: 检测文件格式
2025-07-29 17:48:01 - INFO -   文件扩展名: .xlsx
2025-07-29 17:48:01 - INFO -   实际格式: xls
2025-07-29 17:48:01 - INFO -   ✓ 创建格式正确的文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org_corrected.xls
2025-07-29 17:48:01 - INFO -   使用修复后的文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org_corrected.xls
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx] 步骤 3: 使用修复后的文件
2025-07-29 17:48:01 - INFO -   使用引擎: xlrd
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx] 步骤 4: 使用 xlrd 引擎读取
2025-07-29 17:48:01 - INFO -   文件读取成功，共 147 行 14 列
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx] 步骤 5: 文件读取成功: 147 行 14 列
2025-07-29 17:48:01 - INFO -   找到Package列: 'Package'
2025-07-29 17:48:01 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:48:01 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:48:01 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:48:01 - INFO -   列名识别结果:
2025-07-29 17:48:01 - INFO -     Package列: Package
2025-07-29 17:48:01 - INFO -     CausedBy列: CausedBy
2025-07-29 17:48:01 - INFO -     Version列: Version
2025-07-29 17:48:01 - INFO -     Path列: Path
2025-07-29 17:48:01 - INFO -   开始数据预处理...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx] 步骤 6: 开始数据预处理
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx] 步骤 7: 加载Package-Owner映射
2025-07-29 17:48:01 - INFO -   开始批量处理 147 行数据...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx] 步骤 8: 开始批量处理 147 行数据
2025-07-29 17:48:01 - INFO -   分析完成:
2025-07-29 17:48:01 - INFO -     总记录数: 147
2025-07-29 17:48:01 - INFO -     唯一Package数: 7
2025-07-29 17:48:01 - INFO -     涉及团队数: 3
2025-07-29 17:48:01 - INFO -     未知团队记录: 56
2025-07-29 17:48:01 - INFO -     Top 5 Package:
2025-07-29 17:48:01 - INFO -       com.android.chrome: 90 条记录
2025-07-29 17:48:01 - INFO -       com.google.android.gm: 35 条记录
2025-07-29 17:48:01 - INFO -       com.gallery20: 14 条记录
2025-07-29 17:48:01 - INFO -       com.transsion.autotest.fillphonetool2: 4 条记录
2025-07-29 17:48:01 - INFO -       /vendor/bin/hw/android.hardware.audio.service.mediatek: 2 条记录
2025-07-29 17:48:01 - INFO - Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org_corrected.xls - 成功 - 147 条记录
2025-07-29 17:48:01 - INFO - 操作完成: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx - 分析完成: 147 条记录 (耗时: 0.027s, 步骤数: 8)
2025-07-29 17:48:01 - INFO -   ✓ 成功分析，获得 147 条记录
2025-07-29 17:48:01 - INFO - 
[6/21] 处理文件: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx
2025-07-29 17:48:01 - INFO - 开始操作: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx
2025-07-29 17:48:01 - INFO - 开始分析Excel文件: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx] 步骤 1: 开始分析文件
2025-07-29 17:48:01 - INFO -   检测文件格式...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx] 步骤 2: 检测文件格式
2025-07-29 17:48:01 - INFO -   文件扩展名: .xlsx
2025-07-29 17:48:01 - INFO -   实际格式: xls
2025-07-29 17:48:01 - INFO -   ✓ 创建格式正确的文件: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls
2025-07-29 17:48:01 - INFO -   使用修复后的文件: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx] 步骤 3: 使用修复后的文件
2025-07-29 17:48:01 - INFO -   使用引擎: xlrd
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx] 步骤 4: 使用 xlrd 引擎读取
2025-07-29 17:48:01 - INFO -   文件读取成功，共 3 行 12 列
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx] 步骤 5: 文件读取成功: 3 行 12 列
2025-07-29 17:48:01 - INFO -   找到Package列: 'Package'
2025-07-29 17:48:01 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:48:01 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:48:01 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:48:01 - INFO -   列名识别结果:
2025-07-29 17:48:01 - INFO -     Package列: Package
2025-07-29 17:48:01 - INFO -     CausedBy列: CausedBy
2025-07-29 17:48:01 - INFO -     Version列: Version
2025-07-29 17:48:01 - INFO -     Path列: Path
2025-07-29 17:48:01 - INFO -   开始数据预处理...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx] 步骤 6: 开始数据预处理
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx] 步骤 7: 加载Package-Owner映射
2025-07-29 17:48:01 - INFO -   开始批量处理 3 行数据...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx] 步骤 8: 开始批量处理 3 行数据
2025-07-29 17:48:01 - INFO -   分析完成:
2025-07-29 17:48:01 - INFO -     总记录数: 3
2025-07-29 17:48:01 - INFO -     唯一Package数: 3
2025-07-29 17:48:01 - INFO -     涉及团队数: 2
2025-07-29 17:48:01 - INFO -     未知团队记录: 2
2025-07-29 17:48:01 - INFO -     Top 5 Package:
2025-07-29 17:48:01 - INFO -       com.android.settings: 1 条记录
2025-07-29 17:48:01 - INFO -       com.transsion.cloudserver: 1 条记录
2025-07-29 17:48:01 - INFO -       com.trassion.infinix.xclub: 1 条记录
2025-07-29 17:48:01 - INFO - Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls - 成功 - 3 条记录
2025-07-29 17:48:01 - INFO - 操作完成: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx - 分析完成: 3 条记录 (耗时: 0.020s, 步骤数: 8)
2025-07-29 17:48:01 - INFO -   ✓ 成功分析，获得 3 条记录
2025-07-29 17:48:01 - INFO - 
[7/21] 处理文件: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls
2025-07-29 17:48:01 - INFO - 开始操作: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls
2025-07-29 17:48:01 - INFO - 开始分析Excel文件: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls] 步骤 1: 开始分析文件
2025-07-29 17:48:01 - INFO -   检测文件格式...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls] 步骤 2: 检测文件格式
2025-07-29 17:48:01 - INFO -   文件扩展名: .xls
2025-07-29 17:48:01 - INFO -   实际格式: xls
2025-07-29 17:48:01 - INFO -   使用引擎: xlrd
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls] 步骤 3: 使用 xlrd 引擎读取
2025-07-29 17:48:01 - INFO -   文件读取成功，共 2 行 12 列
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls] 步骤 4: 文件读取成功: 2 行 12 列
2025-07-29 17:48:01 - INFO -   找到Package列: 'Package'
2025-07-29 17:48:01 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:48:01 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:48:01 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:48:01 - INFO -   列名识别结果:
2025-07-29 17:48:01 - INFO -     Package列: Package
2025-07-29 17:48:01 - INFO -     CausedBy列: CausedBy
2025-07-29 17:48:01 - INFO -     Version列: Version
2025-07-29 17:48:01 - INFO -     Path列: Path
2025-07-29 17:48:01 - INFO -   开始数据预处理...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls] 步骤 5: 开始数据预处理
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls] 步骤 6: 加载Package-Owner映射
2025-07-29 17:48:01 - INFO -   开始批量处理 2 行数据...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls] 步骤 7: 开始批量处理 2 行数据
2025-07-29 17:48:01 - INFO -   分析完成:
2025-07-29 17:48:01 - INFO -     总记录数: 2
2025-07-29 17:48:01 - INFO -     唯一Package数: 2
2025-07-29 17:48:01 - INFO -     涉及团队数: 2
2025-07-29 17:48:01 - INFO -     未知团队记录: 1
2025-07-29 17:48:01 - INFO -     Top 5 Package:
2025-07-29 17:48:01 - INFO -       com.android.settings: 1 条记录
2025-07-29 17:48:01 - INFO -       com.trassion.infinix.xclub: 1 条记录
2025-07-29 17:48:01 - INFO - Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls - 成功 - 2 条记录
2025-07-29 17:48:01 - INFO - 操作完成: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls - 分析完成: 2 条记录 (耗时: 0.016s, 步骤数: 7)
2025-07-29 17:48:01 - INFO -   ✓ 成功分析，获得 2 条记录
2025-07-29 17:48:01 - INFO - 
[8/21] 处理文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls
2025-07-29 17:48:01 - INFO - 开始操作: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls
2025-07-29 17:48:01 - INFO - 开始分析Excel文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls] 步骤 1: 开始分析文件
2025-07-29 17:48:01 - INFO -   检测文件格式...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls] 步骤 2: 检测文件格式
2025-07-29 17:48:01 - INFO -   文件扩展名: .xls
2025-07-29 17:48:01 - INFO -   实际格式: xls
2025-07-29 17:48:01 - INFO -   使用引擎: xlrd
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls] 步骤 3: 使用 xlrd 引擎读取
2025-07-29 17:48:01 - INFO -   文件读取成功，共 89 行 14 列
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls] 步骤 4: 文件读取成功: 89 行 14 列
2025-07-29 17:48:01 - INFO -   找到Package列: 'Package'
2025-07-29 17:48:01 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:48:01 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:48:01 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:48:01 - INFO -   列名识别结果:
2025-07-29 17:48:01 - INFO -     Package列: Package
2025-07-29 17:48:01 - INFO -     CausedBy列: CausedBy
2025-07-29 17:48:01 - INFO -     Version列: Version
2025-07-29 17:48:01 - INFO -     Path列: Path
2025-07-29 17:48:01 - INFO -   开始数据预处理...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls] 步骤 5: 开始数据预处理
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls] 步骤 6: 加载Package-Owner映射
2025-07-29 17:48:01 - INFO -   开始批量处理 89 行数据...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls] 步骤 7: 开始批量处理 89 行数据
2025-07-29 17:48:01 - INFO -   分析完成:
2025-07-29 17:48:01 - INFO -     总记录数: 89
2025-07-29 17:48:01 - INFO -     唯一Package数: 16
2025-07-29 17:48:01 - INFO -     涉及团队数: 7
2025-07-29 17:48:01 - INFO -     未知团队记录: 19
2025-07-29 17:48:01 - INFO -     Top 5 Package:
2025-07-29 17:48:01 - INFO -       com.android.settings: 46 条记录
2025-07-29 17:48:01 - INFO -       com.android.phone: 9 条记录
2025-07-29 17:48:01 - INFO -       com.android.chrome: 9 条记录
2025-07-29 17:48:01 - INFO -       com.android.settings.intelligence: 5 条记录
2025-07-29 17:48:01 - INFO -       com.transsion.launcher3: 4 条记录
2025-07-29 17:48:01 - INFO - Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls - 成功 - 89 条记录
2025-07-29 17:48:01 - INFO - 操作完成: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls - 分析完成: 89 条记录 (耗时: 0.023s, 步骤数: 7)
2025-07-29 17:48:01 - INFO -   ✓ 成功分析，获得 89 条记录
2025-07-29 17:48:01 - INFO - 
[9/21] 处理文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_corrected.xls
2025-07-29 17:48:01 - INFO - 开始操作: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_corrected.xls
2025-07-29 17:48:01 - INFO - 开始分析Excel文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_corrected.xls
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_corrected.xls] 步骤 1: 开始分析文件
2025-07-29 17:48:01 - INFO -   检测文件格式...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_corrected.xls] 步骤 2: 检测文件格式
2025-07-29 17:48:01 - INFO -   文件扩展名: .xls
2025-07-29 17:48:01 - INFO -   实际格式: xls
2025-07-29 17:48:01 - INFO -   使用引擎: xlrd
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_corrected.xls] 步骤 3: 使用 xlrd 引擎读取
2025-07-29 17:48:01 - INFO -   文件读取成功，共 23 行 14 列
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_corrected.xls] 步骤 4: 文件读取成功: 23 行 14 列
2025-07-29 17:48:01 - INFO -   找到Package列: 'Package'
2025-07-29 17:48:01 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:48:01 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:48:01 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:48:01 - INFO -   列名识别结果:
2025-07-29 17:48:01 - INFO -     Package列: Package
2025-07-29 17:48:01 - INFO -     CausedBy列: CausedBy
2025-07-29 17:48:01 - INFO -     Version列: Version
2025-07-29 17:48:01 - INFO -     Path列: Path
2025-07-29 17:48:01 - INFO -   开始数据预处理...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_corrected.xls] 步骤 5: 开始数据预处理
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_corrected.xls] 步骤 6: 加载Package-Owner映射
2025-07-29 17:48:01 - INFO -   开始批量处理 23 行数据...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_corrected.xls] 步骤 7: 开始批量处理 23 行数据
2025-07-29 17:48:01 - INFO -   分析完成:
2025-07-29 17:48:01 - INFO -     总记录数: 23
2025-07-29 17:48:01 - INFO -     唯一Package数: 8
2025-07-29 17:48:01 - INFO -     涉及团队数: 4
2025-07-29 17:48:01 - INFO -     未知团队记录: 7
2025-07-29 17:48:01 - INFO -     Top 5 Package:
2025-07-29 17:48:01 - INFO -       com.android.settings: 10 条记录
2025-07-29 17:48:01 - INFO -       com.android.phone: 4 条记录
2025-07-29 17:48:01 - INFO -       com.android.settings.intelligence: 2 条记录
2025-07-29 17:48:01 - INFO -       com.android.systemui: 2 条记录
2025-07-29 17:48:01 - INFO -       com.transsion.launcher3: 2 条记录
2025-07-29 17:48:01 - INFO - Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_corrected.xls - 成功 - 23 条记录
2025-07-29 17:48:01 - INFO - 操作完成: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_corrected.xls - 分析完成: 23 条记录 (耗时: 0.023s, 步骤数: 7)
2025-07-29 17:48:01 - INFO -   ✓ 成功分析，获得 23 条记录
2025-07-29 17:48:01 - INFO - 
[10/21] 处理文件: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls
2025-07-29 17:48:01 - INFO - 开始操作: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls
2025-07-29 17:48:01 - INFO - 开始分析Excel文件: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls] 步骤 1: 开始分析文件
2025-07-29 17:48:01 - INFO -   检测文件格式...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls] 步骤 2: 检测文件格式
2025-07-29 17:48:01 - INFO -   文件扩展名: .xls
2025-07-29 17:48:01 - INFO -   实际格式: xls
2025-07-29 17:48:01 - INFO -   使用引擎: xlrd
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls] 步骤 3: 使用 xlrd 引擎读取
2025-07-29 17:48:01 - INFO -   文件读取成功，共 2 行 12 列
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls] 步骤 4: 文件读取成功: 2 行 12 列
2025-07-29 17:48:01 - INFO -   找到Package列: 'Package'
2025-07-29 17:48:01 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:48:01 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:48:01 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:48:01 - INFO -   列名识别结果:
2025-07-29 17:48:01 - INFO -     Package列: Package
2025-07-29 17:48:01 - INFO -     CausedBy列: CausedBy
2025-07-29 17:48:01 - INFO -     Version列: Version
2025-07-29 17:48:01 - INFO -     Path列: Path
2025-07-29 17:48:01 - INFO -   开始数据预处理...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls] 步骤 5: 开始数据预处理
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls] 步骤 6: 加载Package-Owner映射
2025-07-29 17:48:01 - INFO -   开始批量处理 2 行数据...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls] 步骤 7: 开始批量处理 2 行数据
2025-07-29 17:48:01 - INFO -   分析完成:
2025-07-29 17:48:01 - INFO -     总记录数: 2
2025-07-29 17:48:01 - INFO -     唯一Package数: 2
2025-07-29 17:48:01 - INFO -     涉及团队数: 2
2025-07-29 17:48:01 - INFO -     未知团队记录: 1
2025-07-29 17:48:01 - INFO -     Top 5 Package:
2025-07-29 17:48:01 - INFO -       com.android.settings: 1 条记录
2025-07-29 17:48:01 - INFO -       com.trassion.infinix.xclub: 1 条记录
2025-07-29 17:48:01 - INFO - Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls - 成功 - 2 条记录
2025-07-29 17:48:01 - INFO - 操作完成: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls - 分析完成: 2 条记录 (耗时: 0.021s, 步骤数: 7)
2025-07-29 17:48:01 - INFO -   ✓ 成功分析，获得 2 条记录
2025-07-29 17:48:01 - INFO - 
[11/21] 处理文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls
2025-07-29 17:48:01 - INFO - 开始操作: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls
2025-07-29 17:48:01 - INFO - 开始分析Excel文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls] 步骤 1: 开始分析文件
2025-07-29 17:48:01 - INFO -   检测文件格式...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls] 步骤 2: 检测文件格式
2025-07-29 17:48:01 - INFO -   文件扩展名: .xls
2025-07-29 17:48:01 - INFO -   实际格式: xls
2025-07-29 17:48:01 - INFO -   使用引擎: xlrd
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls] 步骤 3: 使用 xlrd 引擎读取
2025-07-29 17:48:01 - INFO -   文件读取成功，共 7 行 14 列
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls] 步骤 4: 文件读取成功: 7 行 14 列
2025-07-29 17:48:01 - INFO -   找到Package列: 'Package'
2025-07-29 17:48:01 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:48:01 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:48:01 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:48:01 - INFO -   列名识别结果:
2025-07-29 17:48:01 - INFO -     Package列: Package
2025-07-29 17:48:01 - INFO -     CausedBy列: CausedBy
2025-07-29 17:48:01 - INFO -     Version列: Version
2025-07-29 17:48:01 - INFO -     Path列: Path
2025-07-29 17:48:01 - INFO -   开始数据预处理...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls] 步骤 5: 开始数据预处理
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls] 步骤 6: 加载Package-Owner映射
2025-07-29 17:48:01 - INFO -   开始批量处理 7 行数据...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls] 步骤 7: 开始批量处理 7 行数据
2025-07-29 17:48:01 - INFO -   分析完成:
2025-07-29 17:48:01 - INFO -     总记录数: 7
2025-07-29 17:48:01 - INFO -     唯一Package数: 5
2025-07-29 17:48:01 - INFO -     涉及团队数: 1
2025-07-29 17:48:01 - INFO -     未知团队记录: 7
2025-07-29 17:48:01 - INFO -     Top 5 Package:
2025-07-29 17:48:01 - INFO -       com.android.settings.intelligence: 2 条记录
2025-07-29 17:48:01 - INFO -       com.transsion.airtransfer: 2 条记录
2025-07-29 17:48:01 - INFO -       com.transsion.cloudserver: 1 条记录
2025-07-29 17:48:01 - INFO -       com.transsion.launcher3: 1 条记录
2025-07-29 17:48:01 - INFO -       com.transsion.phonemaster: 1 条记录
2025-07-29 17:48:01 - INFO - Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls - 成功 - 7 条记录
2025-07-29 17:48:01 - INFO - 操作完成: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls - 分析完成: 7 条记录 (耗时: 0.022s, 步骤数: 7)
2025-07-29 17:48:01 - INFO -   ✓ 成功分析，获得 7 条记录
2025-07-29 17:48:01 - INFO - 
[12/21] 处理文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx
2025-07-29 17:48:01 - INFO - 开始操作: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx
2025-07-29 17:48:01 - INFO - 开始分析Excel文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 1: 开始分析文件
2025-07-29 17:48:01 - INFO -   检测文件格式...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 2: 检测文件格式
2025-07-29 17:48:01 - INFO -   文件扩展名: .xlsx
2025-07-29 17:48:01 - INFO -   实际格式: xls
2025-07-29 17:48:01 - INFO -   ✓ 创建格式正确的文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_corrected.xls
2025-07-29 17:48:01 - INFO -   使用修复后的文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_corrected.xls
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 3: 使用修复后的文件
2025-07-29 17:48:01 - INFO -   使用引擎: xlrd
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 4: 使用 xlrd 引擎读取
2025-07-29 17:48:01 - INFO -   文件读取成功，共 23 行 14 列
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 5: 文件读取成功: 23 行 14 列
2025-07-29 17:48:01 - INFO -   找到Package列: 'Package'
2025-07-29 17:48:01 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:48:01 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:48:01 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:48:01 - INFO -   列名识别结果:
2025-07-29 17:48:01 - INFO -     Package列: Package
2025-07-29 17:48:01 - INFO -     CausedBy列: CausedBy
2025-07-29 17:48:01 - INFO -     Version列: Version
2025-07-29 17:48:01 - INFO -     Path列: Path
2025-07-29 17:48:01 - INFO -   开始数据预处理...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 6: 开始数据预处理
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 7: 加载Package-Owner映射
2025-07-29 17:48:01 - INFO -   开始批量处理 23 行数据...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx] 步骤 8: 开始批量处理 23 行数据
2025-07-29 17:48:01 - INFO -   分析完成:
2025-07-29 17:48:01 - INFO -     总记录数: 23
2025-07-29 17:48:01 - INFO -     唯一Package数: 8
2025-07-29 17:48:01 - INFO -     涉及团队数: 4
2025-07-29 17:48:01 - INFO -     未知团队记录: 7
2025-07-29 17:48:01 - INFO -     Top 5 Package:
2025-07-29 17:48:01 - INFO -       com.android.settings: 10 条记录
2025-07-29 17:48:01 - INFO -       com.android.phone: 4 条记录
2025-07-29 17:48:01 - INFO -       com.android.settings.intelligence: 2 条记录
2025-07-29 17:48:01 - INFO -       com.android.systemui: 2 条记录
2025-07-29 17:48:01 - INFO -       com.transsion.launcher3: 2 条记录
2025-07-29 17:48:01 - INFO - Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_corrected.xls - 成功 - 23 条记录
2025-07-29 17:48:01 - INFO - 操作完成: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx - 分析完成: 23 条记录 (耗时: 0.027s, 步骤数: 8)
2025-07-29 17:48:01 - INFO -   ✓ 成功分析，获得 23 条记录
2025-07-29 17:48:01 - INFO - 
[13/21] 处理文件: Result_None_None_MonkeyAEE_SH_20250617.xlsx
2025-07-29 17:48:01 - INFO - 开始操作: Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xlsx
2025-07-29 17:48:01 - INFO - 开始分析Excel文件: Result_None_None_MonkeyAEE_SH_20250617.xlsx
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xlsx] 步骤 1: 开始分析文件
2025-07-29 17:48:01 - INFO -   检测文件格式...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xlsx] 步骤 2: 检测文件格式
2025-07-29 17:48:01 - INFO -   文件扩展名: .xlsx
2025-07-29 17:48:01 - INFO -   实际格式: xls
2025-07-29 17:48:01 - INFO -   ✓ 创建格式正确的文件: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls
2025-07-29 17:48:01 - INFO -   使用修复后的文件: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xlsx] 步骤 3: 使用修复后的文件
2025-07-29 17:48:01 - INFO -   使用引擎: xlrd
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xlsx] 步骤 4: 使用 xlrd 引擎读取
2025-07-29 17:48:01 - INFO -   文件读取成功，共 2 行 12 列
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xlsx] 步骤 5: 文件读取成功: 2 行 12 列
2025-07-29 17:48:01 - INFO -   找到Package列: 'Package'
2025-07-29 17:48:01 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:48:01 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:48:01 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:48:01 - INFO -   列名识别结果:
2025-07-29 17:48:01 - INFO -     Package列: Package
2025-07-29 17:48:01 - INFO -     CausedBy列: CausedBy
2025-07-29 17:48:01 - INFO -     Version列: Version
2025-07-29 17:48:01 - INFO -     Path列: Path
2025-07-29 17:48:01 - INFO -   开始数据预处理...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xlsx] 步骤 6: 开始数据预处理
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xlsx] 步骤 7: 加载Package-Owner映射
2025-07-29 17:48:01 - INFO -   开始批量处理 2 行数据...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xlsx] 步骤 8: 开始批量处理 2 行数据
2025-07-29 17:48:01 - INFO -   分析完成:
2025-07-29 17:48:01 - INFO -     总记录数: 2
2025-07-29 17:48:01 - INFO -     唯一Package数: 2
2025-07-29 17:48:01 - INFO -     涉及团队数: 2
2025-07-29 17:48:01 - INFO -     未知团队记录: 1
2025-07-29 17:48:01 - INFO -     Top 5 Package:
2025-07-29 17:48:01 - INFO -       com.android.settings: 1 条记录
2025-07-29 17:48:01 - INFO -       com.trassion.infinix.xclub: 1 条记录
2025-07-29 17:48:01 - INFO - Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls - 成功 - 2 条记录
2025-07-29 17:48:01 - INFO - 操作完成: Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xlsx - 分析完成: 2 条记录 (耗时: 0.024s, 步骤数: 8)
2025-07-29 17:48:01 - INFO -   ✓ 成功分析，获得 2 条记录
2025-07-29 17:48:01 - INFO - 
[14/21] 处理文件: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx
2025-07-29 17:48:01 - INFO - 开始操作: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx
2025-07-29 17:48:01 - INFO - 开始分析Excel文件: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx] 步骤 1: 开始分析文件
2025-07-29 17:48:01 - INFO -   检测文件格式...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx] 步骤 2: 检测文件格式
2025-07-29 17:48:01 - INFO -   文件扩展名: .xlsx
2025-07-29 17:48:01 - INFO -   实际格式: xls
2025-07-29 17:48:01 - INFO -   ✓ 创建格式正确的文件: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls
2025-07-29 17:48:01 - INFO -   使用修复后的文件: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx] 步骤 3: 使用修复后的文件
2025-07-29 17:48:01 - INFO -   使用引擎: xlrd
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx] 步骤 4: 使用 xlrd 引擎读取
2025-07-29 17:48:01 - INFO -   文件读取成功，共 2 行 12 列
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx] 步骤 5: 文件读取成功: 2 行 12 列
2025-07-29 17:48:01 - INFO -   找到Package列: 'Package'
2025-07-29 17:48:01 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:48:01 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:48:01 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:48:01 - INFO -   列名识别结果:
2025-07-29 17:48:01 - INFO -     Package列: Package
2025-07-29 17:48:01 - INFO -     CausedBy列: CausedBy
2025-07-29 17:48:01 - INFO -     Version列: Version
2025-07-29 17:48:01 - INFO -     Path列: Path
2025-07-29 17:48:01 - INFO -   开始数据预处理...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx] 步骤 6: 开始数据预处理
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx] 步骤 7: 加载Package-Owner映射
2025-07-29 17:48:01 - INFO -   开始批量处理 2 行数据...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx] 步骤 8: 开始批量处理 2 行数据
2025-07-29 17:48:01 - INFO -   分析完成:
2025-07-29 17:48:01 - INFO -     总记录数: 2
2025-07-29 17:48:01 - INFO -     唯一Package数: 2
2025-07-29 17:48:01 - INFO -     涉及团队数: 2
2025-07-29 17:48:01 - INFO -     未知团队记录: 1
2025-07-29 17:48:01 - INFO -     Top 5 Package:
2025-07-29 17:48:01 - INFO -       com.android.settings: 1 条记录
2025-07-29 17:48:01 - INFO -       com.trassion.infinix.xclub: 1 条记录
2025-07-29 17:48:01 - INFO - Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls - 成功 - 2 条记录
2025-07-29 17:48:01 - INFO - 操作完成: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx - 分析完成: 2 条记录 (耗时: 0.020s, 步骤数: 8)
2025-07-29 17:48:01 - INFO -   ✓ 成功分析，获得 2 条记录
2025-07-29 17:48:01 - INFO - 
[15/21] 处理文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls
2025-07-29 17:48:01 - INFO - 开始操作: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls
2025-07-29 17:48:01 - INFO - 开始分析Excel文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls] 步骤 1: 开始分析文件
2025-07-29 17:48:01 - INFO -   检测文件格式...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls] 步骤 2: 检测文件格式
2025-07-29 17:48:01 - INFO -   文件扩展名: .xls
2025-07-29 17:48:01 - INFO -   实际格式: xls
2025-07-29 17:48:01 - INFO -   使用引擎: xlrd
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls] 步骤 3: 使用 xlrd 引擎读取
2025-07-29 17:48:01 - INFO -   文件读取成功，共 134 行 14 列
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls] 步骤 4: 文件读取成功: 134 行 14 列
2025-07-29 17:48:01 - INFO -   找到Package列: 'Package'
2025-07-29 17:48:01 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:48:01 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:48:01 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:48:01 - INFO -   列名识别结果:
2025-07-29 17:48:01 - INFO -     Package列: Package
2025-07-29 17:48:01 - INFO -     CausedBy列: CausedBy
2025-07-29 17:48:01 - INFO -     Version列: Version
2025-07-29 17:48:01 - INFO -     Path列: Path
2025-07-29 17:48:01 - INFO -   开始数据预处理...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls] 步骤 5: 开始数据预处理
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls] 步骤 6: 加载Package-Owner映射
2025-07-29 17:48:01 - INFO -   开始批量处理 134 行数据...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls] 步骤 7: 开始批量处理 134 行数据
2025-07-29 17:48:01 - INFO -   分析完成:
2025-07-29 17:48:01 - INFO -     总记录数: 134
2025-07-29 17:48:01 - INFO -     唯一Package数: 11
2025-07-29 17:48:01 - INFO -     涉及团队数: 3
2025-07-29 17:48:01 - INFO -     未知团队记录: 117
2025-07-29 17:48:01 - INFO -     Top 5 Package:
2025-07-29 17:48:01 - INFO -       com.google.android.apps.nbu.files: 37 条记录
2025-07-29 17:48:01 - INFO -       com.transsion.airtransfer: 30 条记录
2025-07-29 17:48:01 - INFO -       com.transsion.cloudserver: 28 条记录
2025-07-29 17:48:01 - INFO -       com.android.chrome: 16 条记录
2025-07-29 17:48:01 - INFO -       com.google.android.gm: 9 条记录
2025-07-29 17:48:01 - INFO - Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls - 成功 - 134 条记录
2025-07-29 17:48:01 - INFO - 操作完成: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls - 分析完成: 134 条记录 (耗时: 0.025s, 步骤数: 7)
2025-07-29 17:48:01 - INFO -   ✓ 成功分析，获得 134 条记录
2025-07-29 17:48:01 - INFO - 
[16/21] 处理文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls
2025-07-29 17:48:01 - INFO - 开始操作: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls
2025-07-29 17:48:01 - INFO - 开始分析Excel文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls] 步骤 1: 开始分析文件
2025-07-29 17:48:01 - INFO -   检测文件格式...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls] 步骤 2: 检测文件格式
2025-07-29 17:48:01 - INFO -   文件扩展名: .xls
2025-07-29 17:48:01 - INFO -   实际格式: xls
2025-07-29 17:48:01 - INFO -   使用引擎: xlrd
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls] 步骤 3: 使用 xlrd 引擎读取
2025-07-29 17:48:01 - INFO -   文件读取成功，共 29 行 14 列
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls] 步骤 4: 文件读取成功: 29 行 14 列
2025-07-29 17:48:01 - INFO -   找到Package列: 'Package'
2025-07-29 17:48:01 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:48:01 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:48:01 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:48:01 - INFO -   列名识别结果:
2025-07-29 17:48:01 - INFO -     Package列: Package
2025-07-29 17:48:01 - INFO -     CausedBy列: CausedBy
2025-07-29 17:48:01 - INFO -     Version列: Version
2025-07-29 17:48:01 - INFO -     Path列: Path
2025-07-29 17:48:01 - INFO -   开始数据预处理...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls] 步骤 5: 开始数据预处理
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls] 步骤 6: 加载Package-Owner映射
2025-07-29 17:48:01 - INFO -   开始批量处理 29 行数据...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls] 步骤 7: 开始批量处理 29 行数据
2025-07-29 17:48:01 - INFO -   分析完成:
2025-07-29 17:48:01 - INFO -     总记录数: 29
2025-07-29 17:48:01 - INFO -     唯一Package数: 14
2025-07-29 17:48:01 - INFO -     涉及团队数: 4
2025-07-29 17:48:01 - INFO -     未知团队记录: 23
2025-07-29 17:48:01 - INFO -     Top 5 Package:
2025-07-29 17:48:01 - INFO -       com.transsion.airtransfer: 7 条记录
2025-07-29 17:48:01 - INFO -       com.android.settings: 4 条记录
2025-07-29 17:48:01 - INFO -       com.gallery20: 4 条记录
2025-07-29 17:48:01 - INFO -       com.android.settings.intelligence: 3 条记录
2025-07-29 17:48:01 - INFO -       com.transsion.aivoiceassistant: 2 条记录
2025-07-29 17:48:01 - INFO - Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls - 成功 - 29 条记录
2025-07-29 17:48:01 - INFO - 操作完成: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls - 分析完成: 29 条记录 (耗时: 0.018s, 步骤数: 7)
2025-07-29 17:48:01 - INFO -   ✓ 成功分析，获得 29 条记录
2025-07-29 17:48:01 - INFO - 
[17/21] 处理文件: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls
2025-07-29 17:48:01 - INFO - 开始操作: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls
2025-07-29 17:48:01 - INFO - 开始分析Excel文件: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls] 步骤 1: 开始分析文件
2025-07-29 17:48:01 - INFO -   检测文件格式...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls] 步骤 2: 检测文件格式
2025-07-29 17:48:01 - INFO -   文件扩展名: .xls
2025-07-29 17:48:01 - INFO -   实际格式: xls
2025-07-29 17:48:01 - INFO -   使用引擎: xlrd
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls] 步骤 3: 使用 xlrd 引擎读取
2025-07-29 17:48:01 - INFO -   文件读取成功，共 3 行 12 列
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls] 步骤 4: 文件读取成功: 3 行 12 列
2025-07-29 17:48:01 - INFO -   找到Package列: 'Package'
2025-07-29 17:48:01 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:48:01 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:48:01 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:48:01 - INFO -   列名识别结果:
2025-07-29 17:48:01 - INFO -     Package列: Package
2025-07-29 17:48:01 - INFO -     CausedBy列: CausedBy
2025-07-29 17:48:01 - INFO -     Version列: Version
2025-07-29 17:48:01 - INFO -     Path列: Path
2025-07-29 17:48:01 - INFO -   开始数据预处理...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls] 步骤 5: 开始数据预处理
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls] 步骤 6: 加载Package-Owner映射
2025-07-29 17:48:01 - INFO -   开始批量处理 3 行数据...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls] 步骤 7: 开始批量处理 3 行数据
2025-07-29 17:48:01 - INFO -   分析完成:
2025-07-29 17:48:01 - INFO -     总记录数: 3
2025-07-29 17:48:01 - INFO -     唯一Package数: 3
2025-07-29 17:48:01 - INFO -     涉及团队数: 2
2025-07-29 17:48:01 - INFO -     未知团队记录: 2
2025-07-29 17:48:01 - INFO -     Top 5 Package:
2025-07-29 17:48:01 - INFO -       com.android.settings: 1 条记录
2025-07-29 17:48:01 - INFO -       com.transsion.cloudserver: 1 条记录
2025-07-29 17:48:01 - INFO -       com.trassion.infinix.xclub: 1 条记录
2025-07-29 17:48:01 - INFO - Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls - 成功 - 3 条记录
2025-07-29 17:48:01 - INFO - 操作完成: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls - 分析完成: 3 条记录 (耗时: 0.016s, 步骤数: 7)
2025-07-29 17:48:01 - INFO -   ✓ 成功分析，获得 3 条记录
2025-07-29 17:48:01 - INFO - 
[18/21] 处理文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls
2025-07-29 17:48:01 - INFO - 开始操作: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls
2025-07-29 17:48:01 - INFO - 开始分析Excel文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls] 步骤 1: 开始分析文件
2025-07-29 17:48:01 - INFO -   检测文件格式...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls] 步骤 2: 检测文件格式
2025-07-29 17:48:01 - INFO -   文件扩展名: .xls
2025-07-29 17:48:01 - INFO -   实际格式: xls
2025-07-29 17:48:01 - INFO -   使用引擎: xlrd
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls] 步骤 3: 使用 xlrd 引擎读取
2025-07-29 17:48:01 - INFO -   文件读取成功，共 43 行 14 列
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls] 步骤 4: 文件读取成功: 43 行 14 列
2025-07-29 17:48:01 - INFO -   找到Package列: 'Package'
2025-07-29 17:48:01 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:48:01 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:48:01 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:48:01 - INFO -   列名识别结果:
2025-07-29 17:48:01 - INFO -     Package列: Package
2025-07-29 17:48:01 - INFO -     CausedBy列: CausedBy
2025-07-29 17:48:01 - INFO -     Version列: Version
2025-07-29 17:48:01 - INFO -     Path列: Path
2025-07-29 17:48:01 - INFO -   开始数据预处理...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls] 步骤 5: 开始数据预处理
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls] 步骤 6: 加载Package-Owner映射
2025-07-29 17:48:01 - INFO -   开始批量处理 43 行数据...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls] 步骤 7: 开始批量处理 43 行数据
2025-07-29 17:48:01 - INFO -   分析完成:
2025-07-29 17:48:01 - INFO -     总记录数: 43
2025-07-29 17:48:01 - INFO -     唯一Package数: 13
2025-07-29 17:48:01 - INFO -     涉及团队数: 4
2025-07-29 17:48:01 - INFO -     未知团队记录: 19
2025-07-29 17:48:01 - INFO -     Top 5 Package:
2025-07-29 17:48:01 - INFO -       com.android.settings: 18 条记录
2025-07-29 17:48:01 - INFO -       com.transsion.launcher3: 4 条记录
2025-07-29 17:48:01 - INFO -       com.android.chrome: 4 条记录
2025-07-29 17:48:01 - INFO -       com.sh.smart.caller: 3 条记录
2025-07-29 17:48:01 - INFO -       com.transsion.childmode: 3 条记录
2025-07-29 17:48:01 - INFO - Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls - 成功 - 43 条记录
2025-07-29 17:48:01 - INFO - 操作完成: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls - 分析完成: 43 条记录 (耗时: 0.019s, 步骤数: 7)
2025-07-29 17:48:01 - INFO -   ✓ 成功分析，获得 43 条记录
2025-07-29 17:48:01 - INFO - 
[19/21] 处理文件: statistics.txt
2025-07-29 17:48:01 - INFO - 开始操作: Excel分析: statistics.txt
2025-07-29 17:48:01 - INFO - 开始分析Excel文件: statistics.txt
2025-07-29 17:48:01 - INFO - [Excel分析: statistics.txt] 步骤 1: 开始分析文件
2025-07-29 17:48:01 - INFO -   检测文件格式...
2025-07-29 17:48:01 - INFO - [Excel分析: statistics.txt] 步骤 2: 检测文件格式
2025-07-29 17:48:01 - INFO -   文件扩展名: .txt
2025-07-29 17:48:01 - INFO -   实际格式: unknown
2025-07-29 17:48:01 - INFO -   使用引擎: openpyxl
2025-07-29 17:48:01 - INFO - [Excel分析: statistics.txt] 步骤 3: 使用 openpyxl 引擎读取
2025-07-29 17:48:01 - WARNING -   openpyxl 引擎失败，尝试备用引擎: File is not a zip file
2025-07-29 17:48:01 - WARNING - [Excel分析: statistics.txt] 步骤 4: openpyxl 引擎失败，尝试备用引擎
2025-07-29 17:48:01 - INFO -   尝试备用引擎: xlrd
2025-07-29 17:48:01 - ERROR -   分析Excel文件失败 - D:\Monkey\tOS16.0\AIMonkey\2025-07-28\statistics.txt
2025-07-29 17:48:01 - ERROR -     异常详情: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'"
2025-07-29 17:48:01 - ERROR - Excel分析异常: D:\Monkey\tOS16.0\AIMonkey\2025-07-28\statistics.txt
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 893, in analyze_excel_file
    df = pd.read_excel(
         ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 495, in read_excel
    io = ExcelFile(
         ^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 553, in __init__
    super().__init__(
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 572, in load_workbook
    return load_workbook(
           ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 346, in load_workbook
    reader = ExcelReader(filename, read_only, keep_vba,
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 123, in __init__
    self.archive = _validate_archive(fn)
                   ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 95, in _validate_archive
    archive = ZipFile(filename, 'r')
              ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\SystemApplication\Lib\zipfile.py", line 1299, in __init__
    self._RealGetContents()
  File "D:\SystemApplication\Lib\zipfile.py", line 1366, in _RealGetContents
    raise BadZipFile("File is not a zip file")
zipfile.BadZipFile: File is not a zip file

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 908, in analyze_excel_file
    df = pd.read_excel(
         ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 495, in read_excel
    io = ExcelFile(
         ^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_xlrd.py", line 46, in __init__
    super().__init__(
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_xlrd.py", line 63, in load_workbook
    return open_workbook(file_contents=data, **engine_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\__init__.py", line 172, in open_workbook
    bk = open_workbook_xls(
         ^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 79, in open_workbook_xls
    biff_version = bk.getbof(XL_WORKBOOK_GLOBALS)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 1284, in getbof
    bof_error('Expected BOF record; found %r' % self.mem[savpos:savpos+8])
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 1278, in bof_error
    raise XLRDError('Unsupported format, or corrupt file: ' + msg)
xlrd.biffh.XLRDError: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'"
2025-07-29 17:48:01 - ERROR - Excel分析: statistics.txt - 失败 - 分析异常: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'"
2025-07-29 17:48:01 - ERROR - 操作失败: Excel分析: statistics.txt - 分析异常: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'" (耗时: 0.146s, 步骤数: 4)
2025-07-29 17:48:01 - WARNING -   ✗ 分析失败或无有效数据
2025-07-29 17:48:01 - INFO - 
[20/21] 处理文件: statistics.txt
2025-07-29 17:48:01 - INFO - 开始操作: Excel分析: statistics.txt
2025-07-29 17:48:01 - INFO - 开始分析Excel文件: statistics.txt
2025-07-29 17:48:01 - INFO - [Excel分析: statistics.txt] 步骤 1: 开始分析文件
2025-07-29 17:48:01 - INFO -   检测文件格式...
2025-07-29 17:48:01 - INFO - [Excel分析: statistics.txt] 步骤 2: 检测文件格式
2025-07-29 17:48:01 - INFO -   文件扩展名: .txt
2025-07-29 17:48:01 - INFO -   实际格式: unknown
2025-07-29 17:48:01 - INFO -   使用引擎: openpyxl
2025-07-29 17:48:01 - INFO - [Excel分析: statistics.txt] 步骤 3: 使用 openpyxl 引擎读取
2025-07-29 17:48:01 - WARNING -   openpyxl 引擎失败，尝试备用引擎: File is not a zip file
2025-07-29 17:48:01 - WARNING - [Excel分析: statistics.txt] 步骤 4: openpyxl 引擎失败，尝试备用引擎
2025-07-29 17:48:01 - INFO -   尝试备用引擎: xlrd
2025-07-29 17:48:01 - ERROR -   分析Excel文件失败 - D:\Monkey\tOS15.1\AIMonkey\2025-07-28\statistics.txt
2025-07-29 17:48:01 - ERROR -     异常详情: Unsupported format, or corrupt file: Expected BOF record; found b"['CM5', "
2025-07-29 17:48:01 - ERROR - Excel分析异常: D:\Monkey\tOS15.1\AIMonkey\2025-07-28\statistics.txt
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 893, in analyze_excel_file
    df = pd.read_excel(
         ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 495, in read_excel
    io = ExcelFile(
         ^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 553, in __init__
    super().__init__(
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 572, in load_workbook
    return load_workbook(
           ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 346, in load_workbook
    reader = ExcelReader(filename, read_only, keep_vba,
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 123, in __init__
    self.archive = _validate_archive(fn)
                   ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 95, in _validate_archive
    archive = ZipFile(filename, 'r')
              ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\SystemApplication\Lib\zipfile.py", line 1299, in __init__
    self._RealGetContents()
  File "D:\SystemApplication\Lib\zipfile.py", line 1366, in _RealGetContents
    raise BadZipFile("File is not a zip file")
zipfile.BadZipFile: File is not a zip file

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 908, in analyze_excel_file
    df = pd.read_excel(
         ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 495, in read_excel
    io = ExcelFile(
         ^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_xlrd.py", line 46, in __init__
    super().__init__(
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_xlrd.py", line 63, in load_workbook
    return open_workbook(file_contents=data, **engine_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\__init__.py", line 172, in open_workbook
    bk = open_workbook_xls(
         ^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 79, in open_workbook_xls
    biff_version = bk.getbof(XL_WORKBOOK_GLOBALS)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 1284, in getbof
    bof_error('Expected BOF record; found %r' % self.mem[savpos:savpos+8])
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 1278, in bof_error
    raise XLRDError('Unsupported format, or corrupt file: ' + msg)
xlrd.biffh.XLRDError: Unsupported format, or corrupt file: Expected BOF record; found b"['CM5', "
2025-07-29 17:48:01 - ERROR - Excel分析: statistics.txt - 失败 - 分析异常: Unsupported format, or corrupt file: Expected BOF record; found b"['CM5', "
2025-07-29 17:48:01 - ERROR - 操作失败: Excel分析: statistics.txt - 分析异常: Unsupported format, or corrupt file: Expected BOF record; found b"['CM5', " (耗时: 0.016s, 步骤数: 4)
2025-07-29 17:48:01 - WARNING -   ✗ 分析失败或无有效数据
2025-07-29 17:48:01 - INFO - 
[21/21] 处理文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx
2025-07-29 17:48:01 - INFO - 开始操作: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx
2025-07-29 17:48:01 - INFO - 开始分析Excel文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 1: 开始分析文件
2025-07-29 17:48:01 - INFO -   检测文件格式...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 2: 检测文件格式
2025-07-29 17:48:01 - INFO -   文件扩展名: .xlsx
2025-07-29 17:48:01 - INFO -   实际格式: xls
2025-07-29 17:48:01 - INFO -   ✓ 创建格式正确的文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls
2025-07-29 17:48:01 - INFO -   使用修复后的文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 3: 使用修复后的文件
2025-07-29 17:48:01 - INFO -   使用引擎: xlrd
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 4: 使用 xlrd 引擎读取
2025-07-29 17:48:01 - INFO -   文件读取成功，共 134 行 14 列
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 5: 文件读取成功: 134 行 14 列
2025-07-29 17:48:01 - INFO -   找到Package列: 'Package'
2025-07-29 17:48:01 - INFO -   找到异常信息列: 'CausedBy'
2025-07-29 17:48:01 - INFO -   找到版本信息列: 'Version'
2025-07-29 17:48:01 - INFO -   找到路径信息列: 'Path'
2025-07-29 17:48:01 - INFO -   列名识别结果:
2025-07-29 17:48:01 - INFO -     Package列: Package
2025-07-29 17:48:01 - INFO -     CausedBy列: CausedBy
2025-07-29 17:48:01 - INFO -     Version列: Version
2025-07-29 17:48:01 - INFO -     Path列: Path
2025-07-29 17:48:01 - INFO -   开始数据预处理...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 6: 开始数据预处理
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 7: 加载Package-Owner映射
2025-07-29 17:48:01 - INFO -   开始批量处理 134 行数据...
2025-07-29 17:48:01 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx] 步骤 8: 开始批量处理 134 行数据
2025-07-29 17:48:01 - INFO -   分析完成:
2025-07-29 17:48:01 - INFO -     总记录数: 134
2025-07-29 17:48:01 - INFO -     唯一Package数: 11
2025-07-29 17:48:01 - INFO -     涉及团队数: 3
2025-07-29 17:48:01 - INFO -     未知团队记录: 117
2025-07-29 17:48:01 - INFO -     Top 5 Package:
2025-07-29 17:48:01 - INFO -       com.google.android.apps.nbu.files: 37 条记录
2025-07-29 17:48:01 - INFO -       com.transsion.airtransfer: 30 条记录
2025-07-29 17:48:01 - INFO -       com.transsion.cloudserver: 28 条记录
2025-07-29 17:48:01 - INFO -       com.android.chrome: 16 条记录
2025-07-29 17:48:01 - INFO -       com.google.android.gm: 9 条记录
2025-07-29 17:48:01 - INFO - Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls - 成功 - 134 条记录
2025-07-29 17:48:01 - INFO - 操作完成: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx - 分析完成: 134 条记录 (耗时: 0.045s, 步骤数: 8)
2025-07-29 17:48:01 - INFO -   ✓ 成功分析，获得 134 条记录
2025-07-29 17:48:01 - INFO - 
============================================================
2025-07-29 17:48:01 - INFO - 批量分析统计
2025-07-29 17:48:01 - INFO - ============================================================
2025-07-29 17:48:01 - INFO - 处理结果:
2025-07-29 17:48:01 - INFO -   成功文件: 19
2025-07-29 17:48:01 - INFO -   失败文件: 2
2025-07-29 17:48:01 - INFO -   总记录数: 742
2025-07-29 17:48:01 - INFO -   唯一Package数: 39
2025-07-29 17:48:01 - INFO -   涉及团队数: 9
2025-07-29 17:48:01 - INFO -   处理文件数: 12
2025-07-29 17:48:01 - INFO - 
Package分布 (Top 10):
2025-07-29 17:48:01 - INFO -   com.android.chrome: 144 条记录
2025-07-29 17:48:01 - INFO -   com.android.settings: 140 条记录
2025-07-29 17:48:01 - INFO -   com.transsion.airtransfer: 75 条记录
2025-07-29 17:48:01 - INFO -   com.google.android.apps.nbu.files: 74 条记录
2025-07-29 17:48:01 - INFO -   com.transsion.cloudserver: 62 条记录
2025-07-29 17:48:01 - INFO -   com.google.android.gm: 55 条记录
2025-07-29 17:48:01 - INFO -   com.android.phone: 26 条记录
2025-07-29 17:48:01 - INFO -   com.transsion.autotest.fillphonetool2: 20 条记录
2025-07-29 17:48:01 - INFO -   com.android.settings.intelligence: 20 条记录
2025-07-29 17:48:01 - INFO -   com.gallery20: 20 条记录
2025-07-29 17:48:01 - INFO - 
团队分布:
2025-07-29 17:48:01 - INFO -   未知团队: 409 条记录
2025-07-29 17:48:01 - INFO -   浏览器团队: 144 条记录
2025-07-29 17:48:01 - INFO -   系统设置团队: 140 条记录
2025-07-29 17:48:01 - INFO -   电话团队: 26 条记录
2025-07-29 17:48:01 - INFO -   系统UI团队: 11 条记录
2025-07-29 17:48:01 - INFO -   输入法团队: 6 条记录
2025-07-29 17:48:01 - INFO -   Google服务团队: 4 条记录
2025-07-29 17:48:01 - INFO -   应用商店团队: 1 条记录
2025-07-29 17:48:01 - INFO -   相机团队: 1 条记录
2025-07-29 17:48:01 - INFO - 
文件分布:
2025-07-29 17:48:01 - INFO -   Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls: 268 条记录
2025-07-29 17:48:01 - INFO -   Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls: 178 条记录
2025-07-29 17:48:01 - INFO -   Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org_corrected.xls: 147 条记录
2025-07-29 17:48:01 - INFO -   Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_corrected.xls: 46 条记录
2025-07-29 17:48:01 - INFO -   Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls: 43 条记录
2025-07-29 17:48:01 - INFO -   Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls: 29 条记录
2025-07-29 17:48:01 - INFO -   Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_corrected.xls: 8 条记录
2025-07-29 17:48:01 - INFO -   Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls: 7 条记录
2025-07-29 17:48:01 - INFO -   Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls: 6 条记录
2025-07-29 17:48:01 - INFO -   Result_None_None_MonkeyAEE_SH_20250617_corrected.xls: 4 条记录
2025-07-29 17:48:01 - INFO -   Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls: 4 条记录
2025-07-29 17:48:01 - INFO -   Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_corrected.xls: 2 条记录
2025-07-29 17:48:01 - INFO - 批量Excel分析: 总计 21, 成功 19, 失败 2
2025-07-29 17:48:01 - INFO - 操作完成: 批量Excel分析 - 批量分析完成: 成功 19/21 个文件，获得 742 条记录 (耗时: 0.672s, 步骤数: 2)
2025-07-29 17:48:01 - INFO - 分析完成，获得 742 条异常记录
2025-07-29 17:48:01 - INFO - [AIMonkey处理流程: 2025-07-28] 步骤 4: 分析完成: 742 条记录
2025-07-29 17:48:01 - INFO - 
步骤3: 生成摘要报告...
2025-07-29 17:48:01 - INFO - [AIMonkey处理流程: 2025-07-28] 步骤 5: 生成摘要报告
2025-07-29 17:48:01 - INFO - 开始操作: 生成摘要报告
2025-07-29 17:48:01 - INFO - 生成分析摘要报告...
2025-07-29 17:48:01 - INFO - [生成摘要报告] 步骤 1: 开始生成摘要报告
2025-07-29 17:48:01 - INFO - [生成摘要报告] 步骤 2: 分析 742 条记录
2025-07-29 17:48:01 - INFO - [生成摘要报告] 步骤 3: 计算基本统计信息
2025-07-29 17:48:01 - INFO - [生成摘要报告] 步骤 4: 分析Top Package
2025-07-29 17:48:01 - INFO - [生成摘要报告] 步骤 5: 分析团队分布
2025-07-29 17:48:01 - INFO - [生成摘要报告] 步骤 6: 分析文件分布
2025-07-29 17:48:01 - INFO - [生成摘要报告] 步骤 7: 分析异常关键词
2025-07-29 17:48:01 - INFO - ✓ 摘要报告生成完成
2025-07-29 17:48:01 - INFO -   分析日期: 2025-07-28
2025-07-29 17:48:01 - INFO -   总记录数: 742
2025-07-29 17:48:01 - INFO -   涉及Package: 39
2025-07-29 17:48:01 - INFO -   涉及团队: 9
2025-07-29 17:48:01 - INFO - 操作完成: 生成摘要报告 - 摘要报告生成完成: 742 条记录 (耗时: 0.020s, 步骤数: 7)
2025-07-29 17:48:01 - INFO - 摘要报告生成成功
2025-07-29 17:48:01 - INFO - [AIMonkey处理流程: 2025-07-28] 步骤 6: 摘要报告生成成功
2025-07-29 17:48:01 - INFO - 
步骤4: 导出分析结果...
2025-07-29 17:48:01 - INFO - [AIMonkey处理流程: 2025-07-28] 步骤 7: 导出分析结果
2025-07-29 17:48:01 - INFO - 开始操作: 导出分析结果
2025-07-29 17:48:01 - INFO - 开始导出分析结果到: D:\Monkey\aimonkey_analysis_results_20250728.xlsx
2025-07-29 17:48:01 - INFO - [导出分析结果] 步骤 1: 准备导出到: aimonkey_analysis_results_20250728.xlsx
2025-07-29 17:48:01 - INFO - [导出分析结果] 步骤 2: 转换数据: 742 条记录
2025-07-29 17:48:01 - INFO - [导出分析结果] 步骤 3: 写入主数据表
2025-07-29 17:48:01 - INFO - [导出分析结果] 步骤 4: 写入统计信息表
2025-07-29 17:48:01 - INFO - [导出分析结果] 步骤 5: 写入Package分布表
2025-07-29 17:48:01 - INFO - [导出分析结果] 步骤 6: 写入团队分布表
2025-07-29 17:48:01 - INFO - [导出分析结果] 步骤 7: 写入文件分布表
2025-07-29 17:48:01 - INFO - ✓ 成功导出分析结果
2025-07-29 17:48:01 - INFO -   文件路径: D:\Monkey\aimonkey_analysis_results_20250728.xlsx
2025-07-29 17:48:01 - INFO -   文件大小: 65.1 KB
2025-07-29 17:48:01 - INFO -   包含工作表: 分析结果, 统计信息, Package分布, 团队分布, 文件分布
2025-07-29 17:48:01 - INFO - 文件操作: 导出 - D:\Monkey\aimonkey_analysis_results_20250728.xlsx - 成功 - 大小: 65.1 KB
2025-07-29 17:48:01 - INFO - 操作完成: 导出分析结果 - 导出成功: 65.1 KB (耗时: 0.089s, 步骤数: 7)
2025-07-29 17:48:01 - INFO - 分析结果已导出到: aimonkey_analysis_results_20250728.xlsx
2025-07-29 17:48:01 - INFO - [AIMonkey处理流程: 2025-07-28] 步骤 8: 导出成功: aimonkey_analysis_results_20250728.xlsx
2025-07-29 17:48:01 - INFO - 
步骤5: 按owner分组异常信息...
2025-07-29 17:48:01 - INFO - [AIMonkey处理流程: 2025-07-28] 步骤 9: 按owner分组异常信息
2025-07-29 17:48:01 - INFO - 异常信息已按 9 个团队分组
2025-07-29 17:48:01 - INFO - [AIMonkey处理流程: 2025-07-28] 步骤 10: 分组完成: 9 个团队
2025-07-29 17:48:01 - INFO - 
步骤6: 发送飞书通知...
2025-07-29 17:48:01 - INFO - [AIMonkey处理流程: 2025-07-28] 步骤 11: 发送飞书通知
2025-07-29 17:48:01 - INFO - 开始操作: 发送飞书通知
2025-07-29 17:48:01 - INFO - 准备发送通知给 9 个团队
2025-07-29 17:48:01 - INFO - [发送飞书通知] 步骤 1: 准备发送 9 个通知
2025-07-29 17:48:01 - INFO - 跳过未知团队 (409 条记录)
2025-07-29 17:48:01 - INFO - 发送通知给: 电话团队 (26 条异常)
2025-07-29 17:48:01 - INFO - [发送飞书通知] 步骤 2: 发送通知给: 电话团队
2025-07-29 17:48:02 - INFO -   ✓ 通知发送成功: None
2025-07-29 17:48:02 - INFO - [发送飞书通知] 步骤 3: 通知发送成功: 电话团队
2025-07-29 17:48:02 - INFO - 发送通知给: 浏览器团队 (144 条异常)
2025-07-29 17:48:02 - INFO - [发送飞书通知] 步骤 4: 发送通知给: 浏览器团队
2025-07-29 17:48:04 - INFO -   ✓ 通知发送成功: None
2025-07-29 17:48:04 - INFO - [发送飞书通知] 步骤 5: 通知发送成功: 浏览器团队
2025-07-29 17:48:04 - INFO - 发送通知给: 系统设置团队 (140 条异常)
2025-07-29 17:48:04 - INFO - [发送飞书通知] 步骤 6: 发送通知给: 系统设置团队
2025-07-29 17:48:06 - INFO -   ✓ 通知发送成功: None
2025-07-29 17:48:06 - INFO - [发送飞书通知] 步骤 7: 通知发送成功: 系统设置团队
2025-07-29 17:48:06 - INFO - 发送通知给: 系统UI团队 (11 条异常)
2025-07-29 17:48:06 - INFO - [发送飞书通知] 步骤 8: 发送通知给: 系统UI团队
2025-07-29 17:48:07 - INFO -   ✓ 通知发送成功: None
2025-07-29 17:48:07 - INFO - [发送飞书通知] 步骤 9: 通知发送成功: 系统UI团队
2025-07-29 17:48:07 - INFO - 发送通知给: 输入法团队 (6 条异常)
2025-07-29 17:48:07 - INFO - [发送飞书通知] 步骤 10: 发送通知给: 输入法团队
2025-07-29 17:48:07 - INFO -   ✓ 通知发送成功: None
2025-07-29 17:48:07 - INFO - [发送飞书通知] 步骤 11: 通知发送成功: 输入法团队
2025-07-29 17:48:07 - INFO - 发送通知给: Google服务团队 (4 条异常)
2025-07-29 17:48:07 - INFO - [发送飞书通知] 步骤 12: 发送通知给: Google服务团队
2025-07-29 17:48:08 - INFO -   ✓ 通知发送成功: None
2025-07-29 17:48:08 - INFO - [发送飞书通知] 步骤 13: 通知发送成功: Google服务团队
2025-07-29 17:48:08 - INFO - 发送通知给: 应用商店团队 (1 条异常)
2025-07-29 17:48:08 - INFO - [发送飞书通知] 步骤 14: 发送通知给: 应用商店团队
2025-07-29 17:48:09 - INFO -   ✓ 通知发送成功: None
2025-07-29 17:48:09 - INFO - [发送飞书通知] 步骤 15: 通知发送成功: 应用商店团队
2025-07-29 17:48:09 - INFO - 发送通知给: 相机团队 (1 条异常)
2025-07-29 17:48:09 - INFO - [发送飞书通知] 步骤 16: 发送通知给: 相机团队
2025-07-29 17:48:09 - INFO -   ✓ 通知发送成功: None
2025-07-29 17:48:09 - INFO - [发送飞书通知] 步骤 17: 通知发送成功: 相机团队
2025-07-29 17:48:09 - INFO - 
通知发送统计:
2025-07-29 17:48:09 - INFO -   成功发送: 8 条
2025-07-29 17:48:09 - INFO -   发送失败: 0 条
2025-07-29 17:48:09 - INFO - 批量通知发送: 总计 9, 成功 8, 失败 0
2025-07-29 17:48:09 - INFO - 操作完成: 发送飞书通知 - 通知发送完成: 成功 8/9 条 (耗时: 7.833s, 步骤数: 17)
2025-07-29 17:48:09 - INFO - 成功发送 8 条通知
2025-07-29 17:48:09 - INFO - [AIMonkey处理流程: 2025-07-28] 步骤 12: 通知发送成功: 8 条
2025-07-29 17:48:09 - INFO - 
================================================================================
2025-07-29 17:48:09 - INFO - 处理流程完成统计
2025-07-29 17:48:09 - INFO - ================================================================================
2025-07-29 17:48:09 - INFO - ✓ 目标日期: 2025-07-28
2025-07-29 17:48:09 - INFO - ✓ 找到文件: 21 个
2025-07-29 17:48:09 - INFO - ✓ 分析文件: 21 个
2025-07-29 17:48:09 - INFO - ✓ 分析记录: 742 条
2025-07-29 17:48:09 - INFO - ✓ 涉及Package: 39 个
2025-07-29 17:48:09 - INFO - ✓ 涉及团队: 9 个
2025-07-29 17:48:09 - INFO - ✓ 发送通知: 8 条
2025-07-29 17:48:09 - INFO - ✓ 导出文件: aimonkey_analysis_results_20250728.xlsx
2025-07-29 17:48:09 - INFO - ================================================================================
2025-07-29 17:48:09 - INFO - AIMonkey异常信息处理流程完成
2025-07-29 17:48:09 - INFO - ================================================================================
2025-07-29 17:48:09 - INFO - 操作完成: AIMonkey处理流程: 2025-07-28 - 处理流程完成: 742 条记录, 8 条通知 (耗时: 15.082s, 步骤数: 12)
2025-07-29 17:56:29 - INFO - AIMonkey工具初始化完成
2025-07-29 17:56:29 - INFO - 重试配置: {'max_retries': 1, 'retry_delay': 2}
2025-07-29 17:56:29 - INFO - 开始操作: AIMonkey处理流程: 2025-07-28
2025-07-29 17:56:29 - INFO - ================================================================================
2025-07-29 17:56:29 - INFO - 开始AIMonkey异常信息处理流程 (目标日期: 2025-07-28)
2025-07-29 17:56:29 - INFO - ================================================================================
2025-07-29 17:56:29 - INFO - 
步骤1: 下载AIMonkey文件...
2025-07-29 17:56:29 - INFO - [AIMonkey处理流程: 2025-07-28] 步骤 1: 开始下载文件
2025-07-29 17:56:29 - INFO - 开始操作: AIMonkey文件下载
2025-07-29 17:56:29 - INFO - ============================================================
2025-07-29 17:56:29 - INFO - 开始下载AIMonkey文件
2025-07-29 17:56:29 - INFO - ============================================================
2025-07-29 17:56:29 - INFO - SMB会话已建立: 10.205.101.200
2025-07-29 17:56:29 - INFO - 目标日期: 2025-07-28
2025-07-29 17:56:29 - INFO - 本地基础目录: D:\Monkey
2025-07-29 17:56:29 - INFO - [AIMonkey文件下载] 步骤 1: 设置目标日期: 2025-07-28
2025-07-29 17:56:29 - INFO - 
[1/2] 处理目录: tOS15.1
2025-07-29 17:56:29 - INFO - 远程路径: tOS15.1\AIMonkey\2025-07-28
2025-07-29 17:56:29 - INFO - 本地路径: D:\Monkey\tOS15.1\AIMonkey\2025-07-28
2025-07-29 17:56:29 - INFO - [AIMonkey文件下载] 步骤 2: 处理目录 tOS15.1
2025-07-29 17:56:29 - INFO - 开始操作: SMB文件下载: tOS15.1\AIMonkey\2025-07-28
2025-07-29 17:56:29 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-28] 步骤 1: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-07-28
2025-07-29 17:56:29 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-28] 步骤 2: 连接远程目录: \\10.205.101.200\osptlog1\tOS15.1\AIMonkey\2025-07-28
2025-07-29 17:56:30 - INFO - 找到 9 个文件/目录
2025-07-29 17:56:30 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-28] 步骤 3: 扫描到 9 个文件/目录
2025-07-29 17:56:30 - INFO - file_name: 系统应用
2025-07-29 17:56:30 - INFO - file_name: 桌面
2025-07-29 17:56:30 - INFO - file_name: 门户
2025-07-29 17:56:30 - INFO - file_name: 框架
2025-07-29 17:56:30 - INFO - file_name: 基础服务
2025-07-29 17:56:30 - INFO - file_name: 敏捷
2025-07-29 17:56:30 - INFO - file_name: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xls
2025-07-29 17:56:30 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-28] 步骤 4: 准备下载到本地: D:\Monkey\tOS15.1\AIMonkey\2025-07-28\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx
2025-07-29 17:56:30 - INFO - 开始操作: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xls
2025-07-29 17:56:30 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xls] 步骤 1: 开始下载文件
2025-07-29 17:56:30 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-07-28
2025-07-29 17:56:30 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xls] 步骤 3: 获取文件大小: 392.5 KB
2025-07-29 17:56:30 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xls] 步骤 4: 开始文件传输
2025-07-29 17:56:30 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xls] 步骤 5: 文件传输完成
2025-07-29 17:56:30 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xls] 步骤 6: 下载完成: 392.5 KB
2025-07-29 17:56:30 - INFO - 文件操作: 下载 - D:\Monkey\tOS15.1\AIMonkey\2025-07-28\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx - 成功 - 大小: 392.5 KB
2025-07-29 17:56:30 - INFO - 操作完成: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xls - 文件下载成功: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xls (耗时: 0.536s, 步骤数: 6)
2025-07-29 17:56:30 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-28] 步骤 5: 成功下载: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xls
2025-07-29 17:56:30 - INFO - file_name: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xls
2025-07-29 17:56:30 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-28] 步骤 6: 准备下载到本地: D:\Monkey\tOS15.1\AIMonkey\2025-07-28\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx
2025-07-29 17:56:30 - INFO - 开始操作: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xls
2025-07-29 17:56:30 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xls] 步骤 1: 开始下载文件
2025-07-29 17:56:30 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-07-28
2025-07-29 17:56:30 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xls] 步骤 3: 获取文件大小: 17.5 KB
2025-07-29 17:56:30 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xls] 步骤 4: 开始文件传输
2025-07-29 17:56:30 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xls] 步骤 5: 文件传输完成
2025-07-29 17:56:30 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xls] 步骤 6: 下载完成: 17.5 KB
2025-07-29 17:56:30 - INFO - 文件操作: 下载 - D:\Monkey\tOS15.1\AIMonkey\2025-07-28\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx - 成功 - 大小: 17.5 KB
2025-07-29 17:56:30 - INFO - 操作完成: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xls - 文件下载成功: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xls (耗时: 0.150s, 步骤数: 6)
2025-07-29 17:56:30 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-28] 步骤 7: 成功下载: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xls
2025-07-29 17:56:30 - INFO - file_name: statistics.txt
2025-07-29 17:56:30 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-28] 步骤 8: 准备下载到本地: D:\Monkey\tOS15.1\AIMonkey\2025-07-28\statistics.txt
2025-07-29 17:56:30 - INFO - 开始操作: 下载文件: statistics.txt
2025-07-29 17:56:30 - INFO - [下载文件: statistics.txt] 步骤 1: 开始下载文件
2025-07-29 17:56:30 - INFO - [下载文件: statistics.txt] 步骤 2: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-07-28
2025-07-29 17:56:30 - INFO - [下载文件: statistics.txt] 步骤 3: 获取文件大小: 354.0 B
2025-07-29 17:56:30 - INFO - [下载文件: statistics.txt] 步骤 4: 开始文件传输
2025-07-29 17:56:30 - INFO - [下载文件: statistics.txt] 步骤 5: 文件传输完成
2025-07-29 17:56:30 - INFO - [下载文件: statistics.txt] 步骤 6: 下载完成: 354.0 B
2025-07-29 17:56:30 - INFO - 文件操作: 下载 - D:\Monkey\tOS15.1\AIMonkey\2025-07-28\statistics.txt - 成功 - 大小: 354.0 B
2025-07-29 17:56:30 - INFO - 操作完成: 下载文件: statistics.txt - 文件下载成功: statistics.txt (耗时: 0.125s, 步骤数: 6)
2025-07-29 17:56:30 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-28] 步骤 9: 成功下载: statistics.txt
2025-07-29 17:56:31 - INFO - 操作完成: SMB文件下载: tOS15.1\AIMonkey\2025-07-28 - 下载完成，成功下载 3 个文件 (耗时: 1.124s, 步骤数: 9)
2025-07-29 17:56:31 - INFO - ✓ 成功下载 3 个文件
2025-07-29 17:56:31 - INFO - [AIMonkey文件下载] 步骤 3: 成功下载 3 个文件
2025-07-29 17:56:31 - INFO - 
[2/2] 处理目录: tOS16.0
2025-07-29 17:56:31 - INFO - 远程路径: tOS16.0\AIMonkey\2025-07-28
2025-07-29 17:56:31 - INFO - 本地路径: D:\Monkey\tOS16.0\AIMonkey\2025-07-28
2025-07-29 17:56:31 - INFO - [AIMonkey文件下载] 步骤 4: 处理目录 tOS16.0
2025-07-29 17:56:31 - INFO - 开始操作: SMB文件下载: tOS16.0\AIMonkey\2025-07-28
2025-07-29 17:56:31 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 1: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-07-28
2025-07-29 17:56:31 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 2: 连接远程目录: \\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28
2025-07-29 17:56:31 - INFO - 找到 10 个文件/目录
2025-07-29 17:56:31 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 3: 扫描到 10 个文件/目录
2025-07-29 17:56:31 - INFO - file_name: 门户
2025-07-29 17:56:31 - INFO - file_name: 创新产品
2025-07-29 17:56:31 - INFO - file_name: 基础服务
2025-07-29 17:56:31 - INFO - file_name: 桌面
2025-07-29 17:56:31 - INFO - file_name: 框架
2025-07-29 17:56:31 - INFO - file_name: 系统应用
2025-07-29 17:56:31 - INFO - file_name: 敏捷
2025-07-29 17:56:31 - INFO - file_name: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls
2025-07-29 17:56:31 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 4: 准备下载到本地: D:\Monkey\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xlsx
2025-07-29 17:56:31 - INFO - 开始操作: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls
2025-07-29 17:56:31 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls] 步骤 1: 开始下载文件
2025-07-29 17:56:31 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-07-28
2025-07-29 17:56:31 - WARNING - 下载异常，等待 2 秒后重试: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls - [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls'
2025-07-29 17:56:31 - WARNING - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls] 步骤 3: 下载异常，等待重试: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls'
2025-07-29 17:56:31 - INFO - 第 2 次尝试下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls
2025-07-29 17:56:31 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls] 步骤 4: 重试下载 (第 2 次)
2025-07-29 17:56:33 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls] 步骤 5: 开始下载文件
2025-07-29 17:56:33 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls] 步骤 6: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-07-28
2025-07-29 17:56:33 - ERROR - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls'
2025-07-29 17:56:33 - ERROR - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls' (耗时: 2.116s, 步骤数: 6)
2025-07-29 17:56:33 - ERROR - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls
2025-07-29 17:56:33 - ERROR - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 5: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls
2025-07-29 17:56:33 - INFO - file_name: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls
2025-07-29 17:56:33 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 6: 准备下载到本地: D:\Monkey\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xlsx
2025-07-29 17:56:33 - INFO - 开始操作: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls
2025-07-29 17:56:33 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls] 步骤 1: 开始下载文件
2025-07-29 17:56:33 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-07-28
2025-07-29 17:56:33 - WARNING - 下载异常，等待 2 秒后重试: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls - [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls'
2025-07-29 17:56:33 - WARNING - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls] 步骤 3: 下载异常，等待重试: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls'
2025-07-29 17:56:33 - INFO - 第 2 次尝试下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls
2025-07-29 17:56:33 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls] 步骤 4: 重试下载 (第 2 次)
2025-07-29 17:56:35 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls] 步骤 5: 开始下载文件
2025-07-29 17:56:35 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls] 步骤 6: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-07-28
2025-07-29 17:56:35 - ERROR - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls'
2025-07-29 17:56:35 - ERROR - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls' (耗时: 2.103s, 步骤数: 6)
2025-07-29 17:56:35 - ERROR - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls
2025-07-29 17:56:35 - ERROR - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 7: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls
2025-07-29 17:56:35 - INFO - file_name: statistics.txt
2025-07-29 17:56:35 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 8: 准备下载到本地: D:\Monkey\tOS16.0\AIMonkey\2025-07-28\statistics.txt
2025-07-29 17:56:35 - INFO - 开始操作: 下载文件: statistics.txt
2025-07-29 17:56:35 - INFO - [下载文件: statistics.txt] 步骤 1: 开始下载文件
2025-07-29 17:56:35 - INFO - [下载文件: statistics.txt] 步骤 2: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-07-28
2025-07-29 17:56:35 - INFO - [下载文件: statistics.txt] 步骤 3: 获取文件大小: 374.0 B
2025-07-29 17:56:35 - INFO - [下载文件: statistics.txt] 步骤 4: 开始文件传输
2025-07-29 17:56:35 - INFO - [下载文件: statistics.txt] 步骤 5: 文件传输完成
2025-07-29 17:56:35 - INFO - [下载文件: statistics.txt] 步骤 6: 下载完成: 374.0 B
2025-07-29 17:56:35 - INFO - 文件操作: 下载 - D:\Monkey\tOS16.0\AIMonkey\2025-07-28\statistics.txt - 成功 - 大小: 374.0 B
2025-07-29 17:56:35 - INFO - 操作完成: 下载文件: statistics.txt - 文件下载成功: statistics.txt (耗时: 0.153s, 步骤数: 6)
2025-07-29 17:56:35 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 9: 成功下载: statistics.txt
2025-07-29 17:56:35 - INFO - 操作完成: SMB文件下载: tOS16.0\AIMonkey\2025-07-28 - 下载完成，成功下载 1 个文件 (耗时: 4.574s, 步骤数: 9)
2025-07-29 17:56:35 - INFO - ✓ 成功下载 1 个文件
2025-07-29 17:56:35 - INFO - [AIMonkey文件下载] 步骤 5: 成功下载 1 个文件
2025-07-29 17:56:35 - INFO - 批量文件下载: 总计 2, 成功 4, 失败 0
2025-07-29 17:56:35 - INFO - 
============================================================
2025-07-29 17:56:35 - INFO - 下载完成统计
2025-07-29 17:56:35 - INFO - ============================================================
2025-07-29 17:56:35 - INFO - 总共下载: 4 个文件
2025-07-29 17:56:35 - INFO - 下载失败: 0 个文件
2025-07-29 17:56:35 - INFO - 目标目录数: 2
2025-07-29 17:56:35 - INFO - 
下载的文件列表:
2025-07-29 17:56:35 - INFO -   - Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx (392.5 KB)
2025-07-29 17:56:35 - INFO -   - Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx (17.5 KB)
2025-07-29 17:56:35 - INFO -   - statistics.txt (354.0 B)
2025-07-29 17:56:35 - INFO -   - statistics.txt (374.0 B)
2025-07-29 17:56:35 - INFO - 操作完成: AIMonkey文件下载 - 下载完成，总计 4 个文件 (耗时: 5.930s, 步骤数: 5)
2025-07-29 17:56:35 - INFO - [AIMonkey处理流程: 2025-07-28] 步骤 2: 验证下载文件
2025-07-29 17:56:35 - INFO - 开始操作: 文件验证
2025-07-29 17:56:35 - INFO - [文件验证] 步骤 1: 开始验证 4 个文件
2025-07-29 17:56:35 - INFO - ✓ Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx - 392.5 KB
2025-07-29 17:56:35 - INFO - ✓ Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx - 17.5 KB
2025-07-29 17:56:35 - INFO - ✓ statistics.txt - 354.0 B
2025-07-29 17:56:35 - INFO - ✓ statistics.txt - 374.0 B
2025-07-29 17:56:35 - INFO - 
验证结果:
2025-07-29 17:56:35 - INFO -   总文件数: 4
2025-07-29 17:56:35 - INFO -   有效文件: 4
2025-07-29 17:56:35 - INFO -   无效文件: 0
2025-07-29 17:56:35 - INFO -   缺失文件: 0
2025-07-29 17:56:35 - INFO - 操作完成: 文件验证 - 验证完成: 有效 4/4 个文件 (耗时: 0.006s, 步骤数: 1)
2025-07-29 17:56:35 - INFO - 
步骤2: 智能搜索和分析Excel文件...
2025-07-29 17:56:35 - INFO - [AIMonkey处理流程: 2025-07-28] 步骤 3: 开始智能文件搜索和分析
2025-07-29 17:56:35 - INFO - 开始操作: 搜索Excel文件: AIMonkey
2025-07-29 17:56:35 - INFO - [搜索Excel文件: AIMonkey] 步骤 1: 搜索日期后缀: 20250728
2025-07-29 17:56:35 - INFO - 搜索条件:
2025-07-29 17:56:35 - INFO -   目录: D:\Monkey\tOS15.1\AIMonkey
2025-07-29 17:56:35 - INFO -   日期后缀: 20250728
2025-07-29 17:56:35 - INFO -   关键词: ['MonkeyAEE', 'Result', 'AIMonkey']
2025-07-29 17:56:35 - INFO - [搜索Excel文件: AIMonkey] 步骤 2: 设置搜索条件: 日期=20250728, 关键词=['MonkeyAEE', 'Result', 'AIMonkey']
2025-07-29 17:56:35 - INFO - ✓ 找到匹配文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx
2025-07-29 17:56:35 - INFO - ✓ 找到匹配文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_corrected.xls
2025-07-29 17:56:35 - INFO - ✓ 找到匹配文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx
2025-07-29 17:56:35 - INFO - ✓ 找到匹配文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org_corrected.xls
2025-07-29 17:56:35 - INFO - 在 D:\Monkey\tOS15.1\AIMonkey 中找到 4 个匹配的Excel文件
2025-07-29 17:56:35 - INFO - 找到的文件列表:
2025-07-29 17:56:35 - INFO -   1. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx (17.5 KB)
2025-07-29 17:56:35 - INFO -   2. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_corrected.xls (17.5 KB)
2025-07-29 17:56:35 - INFO -   3. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx (392.5 KB)
2025-07-29 17:56:35 - INFO -   4. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org_corrected.xls (392.5 KB)
2025-07-29 17:56:35 - INFO - 操作完成: 搜索Excel文件: AIMonkey - 找到 4 个匹配文件 (耗时: 0.023s, 步骤数: 2)
2025-07-29 17:56:35 - INFO - 开始操作: 关键词搜索: Result, MonkeyAEE, SH
2025-07-29 17:56:35 - INFO - 关键词搜索配置:
2025-07-29 17:56:35 - INFO -   目录: D:\Monkey\tOS15.1\AIMonkey
2025-07-29 17:56:35 - INFO -   关键词: ['Result', 'MonkeyAEE', 'SH']
2025-07-29 17:56:35 - INFO -   扩展名: ['.xls', '.xlsx']
2025-07-29 17:56:35 - INFO -   日期模式: ['20250728', '2025_07_28', '2025.07.28']
2025-07-29 17:56:35 - INFO - [关键词搜索: Result, MonkeyAEE, SH] 步骤 1: 搜索关键词: ['Result', 'MonkeyAEE', 'SH'], 扩展名: ['.xls', '.xlsx']
2025-07-29 17:56:35 - INFO - 在 D:\Monkey\tOS15.1\AIMonkey 中找到 10 个匹配的文件
2025-07-29 17:56:35 - INFO - 找到的文件（按匹配度排序）:
2025-07-29 17:56:35 - INFO -   1. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728.xlsx (分数: 4)
2025-07-29 17:56:35 - INFO -      大小: 17.5 KB
2025-07-29 17:56:35 - INFO -      匹配: 关键词: Result, 关键词: SH, 日期: 20250728
2025-07-29 17:56:35 - INFO -   2. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_corrected.xls (分数: 4)
2025-07-29 17:56:35 - INFO -      大小: 17.5 KB
2025-07-29 17:56:35 - INFO -      匹配: 关键词: Result, 关键词: SH, 日期: 20250728
2025-07-29 17:56:35 - INFO -   3. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org.xlsx (分数: 4)
2025-07-29 17:56:35 - INFO -      大小: 392.5 KB
2025-07-29 17:56:35 - INFO -      匹配: 关键词: Result, 关键词: SH, 日期: 20250728
2025-07-29 17:56:35 - INFO -   4. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250728_org_corrected.xls (分数: 4)
2025-07-29 17:56:35 - INFO -      大小: 392.5 KB
2025-07-29 17:56:35 - INFO -      匹配: 关键词: Result, 关键词: SH, 日期: 20250728
2025-07-29 17:56:35 - INFO -   5. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls (分数: 2)
2025-07-29 17:56:35 - INFO -      大小: 41.5 KB
2025-07-29 17:56:35 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:56:35 - INFO -   6. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls (分数: 2)
2025-07-29 17:56:35 - INFO -      大小: 150.5 KB
2025-07-29 17:56:35 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:56:35 - INFO -   7. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729.xlsx (分数: 2)
2025-07-29 17:56:35 - INFO -      大小: 17.5 KB
2025-07-29 17:56:35 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:56:35 - INFO -   8. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_corrected.xls (分数: 2)
2025-07-29 17:56:35 - INFO -      大小: 17.5 KB
2025-07-29 17:56:35 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:56:35 - INFO -   9. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org.xlsx (分数: 2)
2025-07-29 17:56:35 - INFO -      大小: 432.5 KB
2025-07-29 17:56:35 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:56:35 - INFO -   10. Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls (分数: 2)
2025-07-29 17:56:35 - INFO -      大小: 432.5 KB
2025-07-29 17:56:35 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:56:35 - INFO - 操作完成: 关键词搜索: Result, MonkeyAEE, SH - 找到 10 个匹配文件 (耗时: 0.021s, 步骤数: 1)
2025-07-29 17:56:35 - INFO - 开始操作: 搜索Excel文件: AIMonkey
2025-07-29 17:56:35 - INFO - [搜索Excel文件: AIMonkey] 步骤 1: 搜索日期后缀: 20250728
2025-07-29 17:56:35 - INFO - 搜索条件:
2025-07-29 17:56:35 - INFO -   目录: D:\Monkey\tOS16.0\AIMonkey
2025-07-29 17:56:35 - INFO -   日期后缀: 20250728
2025-07-29 17:56:35 - INFO -   关键词: ['MonkeyAEE', 'Result', 'AIMonkey']
2025-07-29 17:56:35 - INFO - [搜索Excel文件: AIMonkey] 步骤 2: 设置搜索条件: 日期=20250728, 关键词=['MonkeyAEE', 'Result', 'AIMonkey']
2025-07-29 17:56:35 - INFO - 在 D:\Monkey\tOS16.0\AIMonkey 中找到 0 个匹配的Excel文件
2025-07-29 17:56:35 - INFO - 操作完成: 搜索Excel文件: AIMonkey - 找到 0 个匹配文件 (耗时: 0.006s, 步骤数: 2)
2025-07-29 17:56:35 - INFO - 开始操作: 关键词搜索: Result, MonkeyAEE, SH
2025-07-29 17:56:35 - INFO - 关键词搜索配置:
2025-07-29 17:56:35 - INFO -   目录: D:\Monkey\tOS16.0\AIMonkey
2025-07-29 17:56:35 - INFO -   关键词: ['Result', 'MonkeyAEE', 'SH']
2025-07-29 17:56:35 - INFO -   扩展名: ['.xls', '.xlsx']
2025-07-29 17:56:35 - INFO -   日期模式: ['20250728', '2025_07_28', '2025.07.28']
2025-07-29 17:56:35 - INFO - [关键词搜索: Result, MonkeyAEE, SH] 步骤 1: 搜索关键词: ['Result', 'MonkeyAEE', 'SH'], 扩展名: ['.xls', '.xlsx']
2025-07-29 17:56:35 - INFO - 在 D:\Monkey\tOS16.0\AIMonkey 中找到 11 个匹配的文件
2025-07-29 17:56:35 - INFO - 找到的文件（按匹配度排序）:
2025-07-29 17:56:35 - INFO -   1. Result_None_None_MonkeyAEE_SH_20250617.xlsx (分数: 3)
2025-07-29 17:56:35 - INFO -      大小: 29.5 KB
2025-07-29 17:56:35 - INFO -      匹配: 关键词: Result, 关键词: MonkeyAEE, 关键词: SH
2025-07-29 17:56:35 - INFO -   2. Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx (分数: 3)
2025-07-29 17:56:35 - INFO -      大小: 17.5 KB
2025-07-29 17:56:35 - INFO -      匹配: 关键词: Result, 关键词: MonkeyAEE, 关键词: SH
2025-07-29 17:56:35 - INFO -   3. Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls (分数: 3)
2025-07-29 17:56:35 - INFO -      大小: 17.5 KB
2025-07-29 17:56:35 - INFO -      匹配: 关键词: Result, 关键词: MonkeyAEE, 关键词: SH
2025-07-29 17:56:35 - INFO -   4. Result_None_None_MonkeyAEE_SH_20250617_corrected.xls (分数: 3)
2025-07-29 17:56:35 - INFO -      大小: 29.5 KB
2025-07-29 17:56:35 - INFO -      匹配: 关键词: Result, 关键词: MonkeyAEE, 关键词: SH
2025-07-29 17:56:35 - INFO -   5. Result_None_None_MonkeyAEE_SH_20250617_org.xlsx (分数: 3)
2025-07-29 17:56:35 - INFO -      大小: 21.5 KB
2025-07-29 17:56:35 - INFO -      匹配: 关键词: Result, 关键词: MonkeyAEE, 关键词: SH
2025-07-29 17:56:35 - INFO -   6. Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls (分数: 3)
2025-07-29 17:56:35 - INFO -      大小: 21.5 KB
2025-07-29 17:56:35 - INFO -      匹配: 关键词: Result, 关键词: MonkeyAEE, 关键词: SH
2025-07-29 17:56:35 - INFO -   7. Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls (分数: 2)
2025-07-29 17:56:35 - INFO -      大小: 158.5 KB
2025-07-29 17:56:35 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:56:35 - INFO -   8. Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729.xlsx (分数: 2)
2025-07-29 17:56:35 - INFO -      大小: 182.5 KB
2025-07-29 17:56:35 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:56:35 - INFO -   9. Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_corrected.xls (分数: 2)
2025-07-29 17:56:35 - INFO -      大小: 182.5 KB
2025-07-29 17:56:35 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:56:35 - INFO -   10. Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org.xlsx (分数: 2)
2025-07-29 17:56:35 - INFO -      大小: 457.0 KB
2025-07-29 17:56:35 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:56:35 - INFO -   11. Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250729_org_corrected.xls (分数: 2)
2025-07-29 17:56:35 - INFO -      大小: 457.0 KB
2025-07-29 17:56:35 - INFO -      匹配: 关键词: Result, 关键词: SH
2025-07-29 17:56:35 - INFO - 操作完成: 关键词搜索: Result, MonkeyAEE, SH - 找到 11 个匹配文件 (耗时: 0.031s, 步骤数: 1)
