2025-07-29 17:45:30 - <PERSON><PERSON>onkey - ERROR - log_module.py:108 - error -   分析Excel文件失败 - D:\Monkey\tOS15.1\AIMonkey\2025-07-29\statistics.txt
2025-07-29 17:45:30 - AIMonkey - ERROR - log_module.py:108 - error -     异常详情: Unsupported format, or corrupt file: Expected BOF record; found b"['CM5', "
2025-07-29 17:45:30 - AIMonkey - ERROR - log_module.py:116 - exception - Excel分析异常: D:\Monkey\tOS15.1\AIMonkey\2025-07-29\statistics.txt
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 893, in analyze_excel_file
    df = pd.read_excel(
         ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 495, in read_excel
    io = ExcelFile(
         ^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 553, in __init__
    super().__init__(
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 572, in load_workbook
    return load_workbook(
           ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 346, in load_workbook
    reader = ExcelReader(filename, read_only, keep_vba,
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 123, in __init__
    self.archive = _validate_archive(fn)
                   ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 95, in _validate_archive
    archive = ZipFile(filename, 'r')
              ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\SystemApplication\Lib\zipfile.py", line 1299, in __init__
    self._RealGetContents()
  File "D:\SystemApplication\Lib\zipfile.py", line 1366, in _RealGetContents
    raise BadZipFile("File is not a zip file")
zipfile.BadZipFile: File is not a zip file

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 908, in analyze_excel_file
    df = pd.read_excel(
         ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 495, in read_excel
    io = ExcelFile(
         ^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_xlrd.py", line 46, in __init__
    super().__init__(
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_xlrd.py", line 63, in load_workbook
    return open_workbook(file_contents=data, **engine_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\__init__.py", line 172, in open_workbook
    bk = open_workbook_xls(
         ^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 79, in open_workbook_xls
    biff_version = bk.getbof(XL_WORKBOOK_GLOBALS)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 1284, in getbof
    bof_error('Expected BOF record; found %r' % self.mem[savpos:savpos+8])
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 1278, in bof_error
    raise XLRDError('Unsupported format, or corrupt file: ' + msg)
xlrd.biffh.XLRDError: Unsupported format, or corrupt file: Expected BOF record; found b"['CM5', "
2025-07-29 17:45:30 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: statistics.txt - 失败 - 分析异常: Unsupported format, or corrupt file: Expected BOF record; found b"['CM5', "
2025-07-29 17:45:30 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: statistics.txt - 分析异常: Unsupported format, or corrupt file: Expected BOF record; found b"['CM5', " (耗时: 0.212s, 步骤数: 4)
2025-07-29 17:45:30 - AIMonkey - ERROR - log_module.py:108 - error -   分析Excel文件失败 - D:\Monkey\tOS16.0\AIMonkey\2025-07-29\statistics.txt
2025-07-29 17:45:30 - AIMonkey - ERROR - log_module.py:108 - error -     异常详情: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'"
2025-07-29 17:45:30 - AIMonkey - ERROR - log_module.py:116 - exception - Excel分析异常: D:\Monkey\tOS16.0\AIMonkey\2025-07-29\statistics.txt
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 893, in analyze_excel_file
    df = pd.read_excel(
         ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 495, in read_excel
    io = ExcelFile(
         ^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 553, in __init__
    super().__init__(
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 572, in load_workbook
    return load_workbook(
           ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 346, in load_workbook
    reader = ExcelReader(filename, read_only, keep_vba,
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 123, in __init__
    self.archive = _validate_archive(fn)
                   ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 95, in _validate_archive
    archive = ZipFile(filename, 'r')
              ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\SystemApplication\Lib\zipfile.py", line 1299, in __init__
    self._RealGetContents()
  File "D:\SystemApplication\Lib\zipfile.py", line 1366, in _RealGetContents
    raise BadZipFile("File is not a zip file")
zipfile.BadZipFile: File is not a zip file

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 908, in analyze_excel_file
    df = pd.read_excel(
         ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 495, in read_excel
    io = ExcelFile(
         ^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_xlrd.py", line 46, in __init__
    super().__init__(
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_xlrd.py", line 63, in load_workbook
    return open_workbook(file_contents=data, **engine_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\__init__.py", line 172, in open_workbook
    bk = open_workbook_xls(
         ^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 79, in open_workbook_xls
    biff_version = bk.getbof(XL_WORKBOOK_GLOBALS)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 1284, in getbof
    bof_error('Expected BOF record; found %r' % self.mem[savpos:savpos+8])
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 1278, in bof_error
    raise XLRDError('Unsupported format, or corrupt file: ' + msg)
xlrd.biffh.XLRDError: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'"
2025-07-29 17:45:30 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: statistics.txt - 失败 - 分析异常: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'"
2025-07-29 17:45:30 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: statistics.txt - 分析异常: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'" (耗时: 0.006s, 步骤数: 4)
2025-07-29 17:47:58 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls'
2025-07-29 17:47:58 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls' (耗时: 2.092s, 步骤数: 6)
2025-07-29 17:47:58 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls
2025-07-29 17:47:58 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 5: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls
2025-07-29 17:48:00 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls'
2025-07-29 17:48:00 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls' (耗时: 2.090s, 步骤数: 6)
2025-07-29 17:48:00 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls
2025-07-29 17:48:00 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 7: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls
2025-07-29 17:48:01 - AIMonkey - ERROR - log_module.py:108 - error -   分析Excel文件失败 - D:\Monkey\tOS16.0\AIMonkey\2025-07-28\statistics.txt
2025-07-29 17:48:01 - AIMonkey - ERROR - log_module.py:108 - error -     异常详情: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'"
2025-07-29 17:48:01 - AIMonkey - ERROR - log_module.py:116 - exception - Excel分析异常: D:\Monkey\tOS16.0\AIMonkey\2025-07-28\statistics.txt
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 893, in analyze_excel_file
    df = pd.read_excel(
         ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 495, in read_excel
    io = ExcelFile(
         ^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 553, in __init__
    super().__init__(
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 572, in load_workbook
    return load_workbook(
           ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 346, in load_workbook
    reader = ExcelReader(filename, read_only, keep_vba,
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 123, in __init__
    self.archive = _validate_archive(fn)
                   ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 95, in _validate_archive
    archive = ZipFile(filename, 'r')
              ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\SystemApplication\Lib\zipfile.py", line 1299, in __init__
    self._RealGetContents()
  File "D:\SystemApplication\Lib\zipfile.py", line 1366, in _RealGetContents
    raise BadZipFile("File is not a zip file")
zipfile.BadZipFile: File is not a zip file

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 908, in analyze_excel_file
    df = pd.read_excel(
         ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 495, in read_excel
    io = ExcelFile(
         ^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_xlrd.py", line 46, in __init__
    super().__init__(
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_xlrd.py", line 63, in load_workbook
    return open_workbook(file_contents=data, **engine_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\__init__.py", line 172, in open_workbook
    bk = open_workbook_xls(
         ^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 79, in open_workbook_xls
    biff_version = bk.getbof(XL_WORKBOOK_GLOBALS)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 1284, in getbof
    bof_error('Expected BOF record; found %r' % self.mem[savpos:savpos+8])
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 1278, in bof_error
    raise XLRDError('Unsupported format, or corrupt file: ' + msg)
xlrd.biffh.XLRDError: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'"
2025-07-29 17:48:01 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: statistics.txt - 失败 - 分析异常: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'"
2025-07-29 17:48:01 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: statistics.txt - 分析异常: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'" (耗时: 0.146s, 步骤数: 4)
2025-07-29 17:48:01 - AIMonkey - ERROR - log_module.py:108 - error -   分析Excel文件失败 - D:\Monkey\tOS15.1\AIMonkey\2025-07-28\statistics.txt
2025-07-29 17:48:01 - AIMonkey - ERROR - log_module.py:108 - error -     异常详情: Unsupported format, or corrupt file: Expected BOF record; found b"['CM5', "
2025-07-29 17:48:01 - AIMonkey - ERROR - log_module.py:116 - exception - Excel分析异常: D:\Monkey\tOS15.1\AIMonkey\2025-07-28\statistics.txt
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 893, in analyze_excel_file
    df = pd.read_excel(
         ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 495, in read_excel
    io = ExcelFile(
         ^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 553, in __init__
    super().__init__(
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 572, in load_workbook
    return load_workbook(
           ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 346, in load_workbook
    reader = ExcelReader(filename, read_only, keep_vba,
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 123, in __init__
    self.archive = _validate_archive(fn)
                   ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 95, in _validate_archive
    archive = ZipFile(filename, 'r')
              ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\SystemApplication\Lib\zipfile.py", line 1299, in __init__
    self._RealGetContents()
  File "D:\SystemApplication\Lib\zipfile.py", line 1366, in _RealGetContents
    raise BadZipFile("File is not a zip file")
zipfile.BadZipFile: File is not a zip file

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 908, in analyze_excel_file
    df = pd.read_excel(
         ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 495, in read_excel
    io = ExcelFile(
         ^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_xlrd.py", line 46, in __init__
    super().__init__(
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_xlrd.py", line 63, in load_workbook
    return open_workbook(file_contents=data, **engine_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\__init__.py", line 172, in open_workbook
    bk = open_workbook_xls(
         ^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 79, in open_workbook_xls
    biff_version = bk.getbof(XL_WORKBOOK_GLOBALS)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 1284, in getbof
    bof_error('Expected BOF record; found %r' % self.mem[savpos:savpos+8])
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 1278, in bof_error
    raise XLRDError('Unsupported format, or corrupt file: ' + msg)
xlrd.biffh.XLRDError: Unsupported format, or corrupt file: Expected BOF record; found b"['CM5', "
2025-07-29 17:48:01 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: statistics.txt - 失败 - 分析异常: Unsupported format, or corrupt file: Expected BOF record; found b"['CM5', "
2025-07-29 17:48:01 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: statistics.txt - 分析异常: Unsupported format, or corrupt file: Expected BOF record; found b"['CM5', " (耗时: 0.016s, 步骤数: 4)
2025-07-29 17:56:33 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls'
2025-07-29 17:56:33 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls' (耗时: 2.116s, 步骤数: 6)
2025-07-29 17:56:33 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls
2025-07-29 17:56:33 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 5: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls
2025-07-29 17:56:35 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls'
2025-07-29 17:56:35 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls' (耗时: 2.103s, 步骤数: 6)
2025-07-29 17:56:35 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls
2025-07-29 17:56:35 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 7: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls
2025-07-29 18:02:06 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls'
2025-07-29 18:02:06 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls' (耗时: 2.097s, 步骤数: 6)
2025-07-29 18:02:06 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls
2025-07-29 18:02:06 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 5: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls
2025-07-29 18:02:08 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls'
2025-07-29 18:02:08 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls' (耗时: 2.096s, 步骤数: 6)
2025-07-29 18:02:08 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls
2025-07-29 18:02:08 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 7: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls
2025-07-29 18:11:47 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls'
2025-07-29 18:11:47 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls' (耗时: 2.104s, 步骤数: 6)
2025-07-29 18:11:47 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls
2025-07-29 18:11:47 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 5: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728_org.xls
2025-07-29 18:11:50 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls'
2025-07-29 18:11:50 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-28\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls' (耗时: 2.088s, 步骤数: 6)
2025-07-29 18:11:50 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls
2025-07-29 18:11:50 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-07-28] 步骤 7: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250728.xls
